#!/usr/bin/env python3
"""
اختبار إضافة منتج جديد
"""

import os
import sys
from sqlalchemy import create_engine
from sqlalchemy.orm import Session

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.models import Product, ProductBarcode

def test_add_product():
    """اختبار إضافة منتج جديد"""
    print("🧪 اختبار إضافة منتج جديد...")
    print("=" * 50)
    
    try:
        # الاتصال بقاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
        engine = create_engine(f'sqlite:///{db_path}', echo=False)
        print(f"✅ تم الاتصال بقاعدة البيانات: {db_path}")
        
        # إنشاء منتج تجريبي
        test_product_data = {
            'name': 'منتج تجريبي للاختبار',
            'code': 'TEST001',
            'category': 'اختبار',
            'unit': 'قطعة',
            'barcode': '*************',
            'quantity': 100,
            'min_quantity': 10,
            'purchase_price': 50.0,
            'sale_price': 75.0,
            'description': 'منتج تجريبي لاختبار النظام'
        }
        
        session = None
        try:
            session = Session(engine)
            
            # التحقق من عدم وجود المنتج مسبقاً
            existing = session.query(Product).filter(
                Product.name == test_product_data['name']
            ).first()
            
            if existing:
                print("⚠️ المنتج التجريبي موجود مسبقاً، سيتم حذفه أولاً...")
                # حذف الباركودات المرتبطة
                session.query(ProductBarcode).filter(
                    ProductBarcode.product_id == existing.id
                ).delete()
                # حذف المنتج
                session.delete(existing)
                session.commit()
                print("✅ تم حذف المنتج القديم")
            
            # إنشاء المنتج الجديد
            print("📦 إنشاء منتج جديد...")
            new_product = Product(
                name=test_product_data['name'],
                code=test_product_data['code'],
                category=test_product_data['category'],
                unit=test_product_data['unit'],
                barcode=test_product_data['barcode'],
                quantity=test_product_data['quantity'],
                min_quantity=test_product_data['min_quantity'],
                purchase_price=test_product_data['purchase_price'],
                sale_price=test_product_data['sale_price'],
                description=test_product_data['description'],
                is_active=True
            )
            
            session.add(new_product)
            session.flush()  # للحصول على ID
            
            print(f"✅ تم إنشاء المنتج بـ ID: {new_product.id}")
            
            # إضافة باركودات إضافية
            print("🏷️ إضافة باركودات إضافية...")
            additional_barcodes = [
                {'barcode': '*************', 'description': 'الباركود الأساسي', 'is_primary': True},
                {'barcode': '9876543210987', 'description': 'باركود إضافي 1', 'is_primary': False},
                {'barcode': '5555666677778', 'description': 'باركود إضافي 2', 'is_primary': False}
            ]
            
            for barcode_data in additional_barcodes:
                new_barcode = ProductBarcode(
                    product_id=new_product.id,
                    barcode=barcode_data['barcode'],
                    description=barcode_data['description'],
                    is_primary=barcode_data['is_primary']
                )
                session.add(new_barcode)
            
            # حفظ نهائي
            session.commit()
            
            print("✅ تم حفظ المنتج والباركودات بنجاح!")
            
            # التحقق من الحفظ
            print("🔍 التحقق من الحفظ...")
            saved_product = session.query(Product).filter(
                Product.id == new_product.id
            ).first()
            
            if saved_product:
                print(f"✅ المنتج محفوظ: {saved_product.name}")
                print(f"  📝 الكود: {saved_product.code}")
                print(f"  💰 سعر الشراء: {saved_product.purchase_price}")
                print(f"  💵 سعر البيع: {saved_product.sale_price}")
                print(f"  📦 الكمية: {saved_product.quantity}")
                
                # عرض الباركودات
                barcodes = session.query(ProductBarcode).filter(
                    ProductBarcode.product_id == saved_product.id
                ).all()
                
                print(f"  🏷️ الباركودات ({len(barcodes)}):")
                for barcode in barcodes:
                    primary_text = "أساسي" if barcode.is_primary else "إضافي"
                    print(f"    - {barcode.barcode} ({primary_text})")
                
            else:
                print("❌ فشل في العثور على المنتج المحفوظ")
            
            print("\n" + "=" * 50)
            print("✅ اختبار إضافة المنتج مكتمل بنجاح!")
            print("🎯 يمكنك الآن إضافة منتجات جديدة بدون مشاكل")
            
        except Exception as e:
            if session:
                session.rollback()
            raise e
        
        finally:
            if session:
                session.close()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إضافة المنتج: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_add_product()
