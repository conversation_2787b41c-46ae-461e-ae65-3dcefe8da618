#!/usr/bin/env python3
"""
أداة إصلاح قاعدة البيانات
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import Session

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_database():
    """إصلاح مشاكل قاعدة البيانات"""
    print("🔧 بدء إصلاح قاعدة البيانات...")
    print("=" * 50)
    
    try:
        # الاتصال بقاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
        engine = create_engine(f'sqlite:///{db_path}', echo=False)
        print(f"✅ تم الاتصال بقاعدة البيانات: {db_path}")
        
        with Session(engine) as session:
            # 1. إصلاح الجلسات المعلقة
            print("🔄 إصلاح الجلسات المعلقة...")
            try:
                session.execute(text("PRAGMA journal_mode=WAL;"))
                session.execute(text("PRAGMA synchronous=NORMAL;"))
                session.execute(text("PRAGMA cache_size=10000;"))
                session.execute(text("PRAGMA temp_store=memory;"))
                session.commit()
                print("✅ تم تحسين إعدادات قاعدة البيانات")
            except Exception as e:
                print(f"⚠️ تحذير في تحسين الإعدادات: {e}")
            
            # 2. فحص سلامة قاعدة البيانات
            print("🔍 فحص سلامة قاعدة البيانات...")
            try:
                result = session.execute(text("PRAGMA integrity_check;")).fetchone()
                if result and result[0] == 'ok':
                    print("✅ قاعدة البيانات سليمة")
                else:
                    print(f"⚠️ مشكلة في سلامة قاعدة البيانات: {result}")
            except Exception as e:
                print(f"⚠️ خطأ في فحص السلامة: {e}")
            
            # 3. تنظيف الباركودات المكررة
            print("🧹 تنظيف الباركودات المكررة...")
            try:
                # البحث عن الباركودات المكررة
                duplicates = session.execute(text("""
                    SELECT barcode, COUNT(*) as count 
                    FROM product_barcodes 
                    GROUP BY barcode 
                    HAVING COUNT(*) > 1
                """)).fetchall()
                
                if duplicates:
                    print(f"🔍 وجد {len(duplicates)} باركود مكرر")
                    for barcode, count in duplicates:
                        print(f"  - الباركود '{barcode}' مكرر {count} مرة")
                        
                        # حذف النسخ المكررة (الاحتفاظ بالأولى فقط)
                        session.execute(text("""
                            DELETE FROM product_barcodes 
                            WHERE id NOT IN (
                                SELECT MIN(id) 
                                FROM product_barcodes 
                                WHERE barcode = :barcode
                            ) AND barcode = :barcode
                        """), {"barcode": barcode})
                    
                    session.commit()
                    print("✅ تم تنظيف الباركودات المكررة")
                else:
                    print("✅ لا توجد باركودات مكررة")
                    
            except Exception as e:
                print(f"⚠️ خطأ في تنظيف الباركودات: {e}")
            
            # 4. تنظيف المنتجات المكررة
            print("🧹 تنظيف المنتجات المكررة...")
            try:
                # البحث عن المنتجات بنفس الاسم
                duplicates = session.execute(text("""
                    SELECT name, COUNT(*) as count 
                    FROM products 
                    GROUP BY name 
                    HAVING COUNT(*) > 1
                """)).fetchall()
                
                if duplicates:
                    print(f"🔍 وجد {len(duplicates)} منتج مكرر")
                    for name, count in duplicates:
                        print(f"  - المنتج '{name}' مكرر {count} مرة")
                else:
                    print("✅ لا توجد منتجات مكررة")
                    
            except Exception as e:
                print(f"⚠️ خطأ في فحص المنتجات المكررة: {e}")
            
            # 5. إعادة فهرسة قاعدة البيانات
            print("📊 إعادة فهرسة قاعدة البيانات...")
            try:
                session.execute(text("REINDEX;"))
                session.commit()
                print("✅ تم إعادة فهرسة قاعدة البيانات")
            except Exception as e:
                print(f"⚠️ خطأ في إعادة الفهرسة: {e}")
            
            # 6. تنظيف المساحة الفارغة
            print("🗑️ تنظيف المساحة الفارغة...")
            try:
                session.execute(text("VACUUM;"))
                print("✅ تم تنظيف المساحة الفارغة")
            except Exception as e:
                print(f"⚠️ خطأ في تنظيف المساحة: {e}")
            
            # 7. إحصائيات نهائية
            print("📊 إحصائيات قاعدة البيانات:")
            try:
                products_count = session.execute(text("SELECT COUNT(*) FROM products")).scalar()
                barcodes_count = session.execute(text("SELECT COUNT(*) FROM product_barcodes")).scalar()
                transactions_count = session.execute(text("SELECT COUNT(*) FROM transactions")).scalar()
                
                print(f"  📦 المنتجات: {products_count}")
                print(f"  🏷️ الباركودات: {barcodes_count}")
                print(f"  💰 المعاملات: {transactions_count}")
                
            except Exception as e:
                print(f"⚠️ خطأ في جلب الإحصائيات: {e}")
        
        print("\n" + "=" * 50)
        print("✅ تم إصلاح قاعدة البيانات بنجاح!")
        print("🎯 يمكنك الآن تشغيل البرنامج بدون مشاكل")
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_database()
