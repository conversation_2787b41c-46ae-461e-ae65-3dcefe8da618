#!/usr/bin/env python3
"""
أداة تنظيف البيانات المكررة
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import Session

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.models import Product, ProductBarcode

def clean_duplicate_products():
    """تنظيف المنتجات المكررة"""
    print("🧹 تنظيف المنتجات المكررة...")
    print("=" * 50)
    
    try:
        # الاتصال بقاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
        engine = create_engine(f'sqlite:///{db_path}', echo=False)
        print(f"✅ تم الاتصال بقاعدة البيانات: {db_path}")
        
        with Session(engine) as session:
            # 1. تنظيف المنتجات بنفس الاسم
            print("🔍 البحث عن المنتجات المكررة بالاسم...")
            
            duplicates_by_name = session.execute(text("""
                SELECT name, COUNT(*) as count, GROUP_CONCAT(id) as ids
                FROM products 
                GROUP BY name 
                HAVING COUNT(*) > 1
            """)).fetchall()
            
            if duplicates_by_name:
                print(f"🔍 وجد {len(duplicates_by_name)} مجموعة منتجات مكررة بالاسم")
                
                for name, count, ids_str in duplicates_by_name:
                    ids = [int(id_str) for id_str in ids_str.split(',')]
                    print(f"  - المنتج '{name}' مكرر {count} مرة (IDs: {ids})")
                    
                    # الاحتفاظ بالأول وحذف الباقي
                    keep_id = ids[0]
                    delete_ids = ids[1:]
                    
                    for delete_id in delete_ids:
                        # حذف الباركودات المرتبطة أولاً
                        session.execute(text("""
                            DELETE FROM product_barcodes 
                            WHERE product_id = :product_id
                        """), {"product_id": delete_id})
                        
                        # حذف المنتج
                        session.execute(text("""
                            DELETE FROM products 
                            WHERE id = :product_id
                        """), {"product_id": delete_id})
                        
                        print(f"    ✅ تم حذف المنتج المكرر (ID: {delete_id})")
                
                session.commit()
                print("✅ تم تنظيف المنتجات المكررة بالاسم")
            else:
                print("✅ لا توجد منتجات مكررة بالاسم")
            
            # 2. تنظيف المنتجات بنفس الكود
            print("\n🔍 البحث عن المنتجات المكررة بالكود...")
            
            duplicates_by_code = session.execute(text("""
                SELECT code, COUNT(*) as count, GROUP_CONCAT(id) as ids
                FROM products 
                WHERE code IS NOT NULL AND code != ''
                GROUP BY code 
                HAVING COUNT(*) > 1
            """)).fetchall()
            
            if duplicates_by_code:
                print(f"🔍 وجد {len(duplicates_by_code)} مجموعة منتجات مكررة بالكود")
                
                for code, count, ids_str in duplicates_by_code:
                    ids = [int(id_str) for id_str in ids_str.split(',')]
                    print(f"  - الكود '{code}' مكرر {count} مرة (IDs: {ids})")
                    
                    # الاحتفاظ بالأول وتعديل أكواد الباقي
                    keep_id = ids[0]
                    modify_ids = ids[1:]
                    
                    for i, modify_id in enumerate(modify_ids):
                        new_code = f"{code}_DUP_{i+1}"
                        session.execute(text("""
                            UPDATE products 
                            SET code = :new_code 
                            WHERE id = :product_id
                        """), {"new_code": new_code, "product_id": modify_id})
                        
                        print(f"    ✅ تم تعديل كود المنتج (ID: {modify_id}) إلى '{new_code}'")
                
                session.commit()
                print("✅ تم تنظيف المنتجات المكررة بالكود")
            else:
                print("✅ لا توجد منتجات مكررة بالكود")
            
            # 3. إحصائيات نهائية
            print("\n📊 إحصائيات نهائية:")
            products_count = session.query(Product).count()
            barcodes_count = session.query(ProductBarcode).count()
            
            print(f"  📦 إجمالي المنتجات: {products_count}")
            print(f"  🏷️ إجمالي الباركودات: {barcodes_count}")
            
            # 4. التحقق من سلامة البيانات
            print("\n🔍 التحقق من سلامة البيانات...")
            
            # التحقق من المنتجات بدون اسم
            empty_names = session.execute(text("""
                SELECT COUNT(*) FROM products 
                WHERE name IS NULL OR name = ''
            """)).scalar()
            
            if empty_names > 0:
                print(f"⚠️ يوجد {empty_names} منتج بدون اسم")
                # إصلاح المنتجات بدون اسم
                session.execute(text("""
                    UPDATE products 
                    SET name = 'منتج غير محدد ' || id 
                    WHERE name IS NULL OR name = ''
                """))
                session.commit()
                print("✅ تم إصلاح المنتجات بدون اسم")
            else:
                print("✅ جميع المنتجات لها أسماء صحيحة")
            
            # التحقق من الأسعار السالبة
            negative_prices = session.execute(text("""
                SELECT COUNT(*) FROM products 
                WHERE purchase_price < 0 OR sale_price < 0
            """)).scalar()
            
            if negative_prices > 0:
                print(f"⚠️ يوجد {negative_prices} منتج بأسعار سالبة")
                # إصلاح الأسعار السالبة
                session.execute(text("""
                    UPDATE products 
                    SET purchase_price = 0 
                    WHERE purchase_price < 0
                """))
                session.execute(text("""
                    UPDATE products 
                    SET sale_price = purchase_price * 1.2 
                    WHERE sale_price < 0
                """))
                session.commit()
                print("✅ تم إصلاح الأسعار السالبة")
            else:
                print("✅ جميع الأسعار صحيحة")
        
        print("\n" + "=" * 50)
        print("✅ تم تنظيف البيانات بنجاح!")
        print("🎯 يمكنك الآن استيراد البيانات بدون مشاكل")
        
    except Exception as e:
        print(f"❌ خطأ في تنظيف البيانات: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    clean_duplicate_products()
