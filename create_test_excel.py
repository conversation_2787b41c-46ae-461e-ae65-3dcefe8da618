#!/usr/bin/env python3
"""
إنشاء ملف Excel تجريبي للاستيراد
"""

import pandas as pd
import os

def create_test_excel():
    """إنشاء ملف Excel تجريبي للاختبار"""
    print("📝 إنشاء ملف Excel تجريبي للاستيراد...")
    
    # بيانات تجريبية للمنتجات
    products_data = {
        'Name': [
            'لابتوب ديل XPS 13',
            'ماوس لوجيتك MX Master',
            'كيبورد ميكانيكي RGB',
            'شاشة سامسونج 24 بوصة',
            'سماعات سوني WH-1000XM4'
        ],
        'Code': [
            'LAPTOP001',
            'MOUSE002', 
            'KEYBOARD003',
            'MONITOR004',
            'HEADPHONE005'
        ],
        'Purchase_Price': [15000, 800, 1200, 3500, 4500],
        'Sale_Price': [18000, 1000, 1500, 4200, 5500],
        'Quantity': [10, 50, 25, 15, 20],
        'Unit': ['قطعة', 'قطعة', 'قطعة', 'قطعة', 'قطعة'],
        'Category': ['أجهزة كمبيوتر', 'ملحقات', 'ملحقات', 'شاشات', 'صوتيات'],
        'Description': [
            'لابتوب ديل XPS 13 بمعالج Intel Core i7',
            'ماوس لاسلكي عالي الدقة',
            'كيبورد ميكانيكي بإضاءة RGB',
            'شاشة LED عالية الدقة 24 بوصة',
            'سماعات لاسلكية بتقنية إلغاء الضوضاء'
        ],
        'Barcode': [
            '1234567890123',
            '2345678901234',
            '3456789012345',
            '4567890123456',
            '5678901234567'
        ]
    }
    
    # إنشاء DataFrame
    df = pd.DataFrame(products_data)
    
    # حفظ الملف
    test_file = os.path.join(os.path.dirname(__file__), 'test_easacc_products.xlsx')
    
    with pd.ExcelWriter(test_file, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Products', index=False)
        
        # إضافة ورقة للعملاء
        customers_data = {
            'Name': ['أحمد محمد', 'فاطمة علي', 'محمد حسن'],
            'Phone': ['01234567890', '01987654321', '01555666777'],
            'Address': ['القاهرة', 'الإسكندرية', 'الجيزة'],
            'Email': ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        }
        customers_df = pd.DataFrame(customers_data)
        customers_df.to_excel(writer, sheet_name='Customers', index=False)
        
        # إضافة ورقة للموردين
        suppliers_data = {
            'Name': ['شركة التقنية المتقدمة', 'مؤسسة الإلكترونيات', 'شركة الحاسوب'],
            'Phone': ['0223456789', '0212345678', '0234567890'],
            'Address': ['القاهرة الجديدة', 'مدينة نصر', '6 أكتوبر'],
            'Email': ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        }
        suppliers_df = pd.DataFrame(suppliers_data)
        suppliers_df.to_excel(writer, sheet_name='Suppliers', index=False)
    
    print(f"✅ تم إنشاء الملف: {test_file}")
    print(f"📊 المحتويات:")
    print(f"  📦 المنتجات: {len(df)} منتج")
    print(f"  👥 العملاء: {len(customers_df)} عميل")
    print(f"  🏭 الموردين: {len(suppliers_df)} مورد")
    
    # عرض عينة من البيانات
    print(f"\n📋 عينة من المنتجات:")
    for i, row in df.head(3).iterrows():
        print(f"  {i+1}. {row['Name']} - {row['Code']} - {row['Sale_Price']} ج.م")
    
    return test_file

if __name__ == "__main__":
    create_test_excel()
