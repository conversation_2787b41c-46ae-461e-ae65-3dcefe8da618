# دليل استيراد البيانات من EasAcc

## 🎯 نظرة عامة
تم إصلاح مشكلة استيراد البيانات من برنامج EasAcc. الآن يمكنك استيراد البيانات بنجاح بدون أخطاء.

## 🔧 الإصلاحات المطبقة

### 1. إصلاح إدارة جلسات قاعدة البيانات
- تحسين إدارة الجلسات لتجنب أخطاء `Session rollback`
- إضافة معالجة أفضل للأخطاء
- استخدام `flush()` و `rollback()` بشكل صحيح

### 2. إصلاح مشكلة البيانات المكررة
- التحقق من الأسماء والأكواد المكررة قبل الإدراج
- إنشاء أكواد فريدة تلقائياً
- معالجة أخطاء `UNIQUE constraint failed`

### 3. تحسين تحويل البيانات
- تنظيف البيانات قبل الإدراج
- إضافة قيم افتراضية آمنة
- التحقق من صحة الأسعار والكميات

## 📋 خطوات الاستيراد

### الخطوة 1: تحضير الملف
1. تأكد من أن ملف Excel يحتوي على الأعمدة التالية للمنتجات:
   - `Name` أو `اسم المنتج`: اسم المنتج (مطلوب)
   - `Code` أو `الكود`: كود المنتج (اختياري)
   - `Purchase_Price` أو `سعر الشراء`: سعر الشراء
   - `Sale_Price` أو `سعر البيع`: سعر البيع
   - `Quantity` أو `الكمية`: الكمية المتاحة
   - `Unit` أو `الوحدة`: وحدة القياس
   - `Category` أو `الفئة`: فئة المنتج
   - `Description` أو `الوصف`: وصف المنتج

### الخطوة 2: فتح أداة الاستيراد
1. من القائمة الرئيسية، اختر **أدوات** → **استيراد من EasAcc**
2. ستفتح نافذة استيراد البيانات

### الخطوة 3: اختيار الملف
1. اختر نوع الملف: **Excel (.xlsx)**
2. انقر على **تصفح** واختر ملف Excel
3. انقر على **الاتصال** لتحميل البيانات

### الخطوة 4: مراجعة البيانات
1. ستظهر البيانات في جداول منفصلة (منتجات، عملاء، موردين)
2. يمكنك تحديد/إلغاء تحديد العناصر التي تريد استيرادها
3. استخدم **تحديد الكل** أو **إلغاء تحديد الكل** حسب الحاجة

### الخطوة 5: بدء الاستيراد
1. تأكد من تفعيل **إنشاء نسخة احتياطية** (مستحسن)
2. انقر على **استيراد البيانات المحددة**
3. انتظر حتى اكتمال العملية

## 🧪 ملف تجريبي
تم إنشاء ملف `test_easacc_products.xlsx` يحتوي على:
- 5 منتجات تجريبية
- 3 عملاء تجريبيين  
- 3 موردين تجريبيين

يمكنك استخدام هذا الملف لاختبار عملية الاستيراد.

## ⚠️ نصائح مهمة

### قبل الاستيراد:
- تأكد من إغلاق ملف Excel قبل الاستيراد
- انشئ نسخة احتياطية من قاعدة البيانات
- تحقق من صحة البيانات في الملف

### أثناء الاستيراد:
- لا تغلق البرنامج أثناء عملية الاستيراد
- راقب رسائل السجل للتأكد من سير العملية
- في حالة ظهور أخطاء، راجع السجل للتفاصيل

### بعد الاستيراد:
- تحقق من البيانات المستوردة في قوائم المنتجات/العملاء/الموردين
- تأكد من صحة الأسعار والكميات
- قم بعمل نسخة احتياطية جديدة

## 🔍 استكشاف الأخطاء

### خطأ "Session rollback"
- **السبب**: مشكلة في جلسة قاعدة البيانات
- **الحل**: تم إصلاحه تلقائياً في النسخة الجديدة

### خطأ "UNIQUE constraint failed"
- **السبب**: بيانات مكررة (اسم أو كود منتج)
- **الحل**: البرنامج يتخطى البيانات المكررة تلقائياً

### خطأ "pyodbc غير متاح"
- **السبب**: مكتبة pyodbc غير مثبتة
- **الحل**: استخدم ملفات Excel أو CSV بدلاً من Access

## 🛠️ أدوات الصيانة

### تنظيف البيانات المكررة
```bash
python clean_duplicates.py
```

### إصلاح قاعدة البيانات
```bash
python fix_database.py
```

### إنشاء ملف تجريبي
```bash
python create_test_excel.py
```

## 📞 الدعم
في حالة مواجهة أي مشاكل:
1. تحقق من ملف السجل في نافذة الاستيراد
2. استخدم أدوات الصيانة المرفقة
3. تأكد من صحة تنسيق ملف Excel

---
**تم التحديث**: 2025-07-12  
**الإصدار**: 2.0 - مع إصلاح مشاكل الاستيراد
