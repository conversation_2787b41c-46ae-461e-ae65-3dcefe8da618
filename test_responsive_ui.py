#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام المتجاوب للواجهة
Test responsive UI system
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QLabel, QDialog, 
                             QGroupBox, QFormLayout, QLineEdit, QTextEdit,
                             QComboBox, QSpinBox, QCheckBox, QTableWidget,
                             QTableWidgetItem, QHeaderView, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont


class ResponsiveTestDialog(QDialog):
    """حوار اختبار النظام المتجاوب"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة الحوار"""
        # استخدام النظام المتجاوب
        try:
            from utils.dialog_utils import setup_responsive_dialog
            setup_responsive_dialog(self, "اختبار الحوار المتجاوب", 500, 400, 700, 550)
        except ImportError:
            self.setWindowTitle("اختبار الحوار المتجاوب")
            self.resize(700, 550)
        
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # مجموعة النماذج
        forms_group = QGroupBox("نماذج الإدخال")
        forms_layout = QFormLayout()
        
        forms_layout.addRow("الاسم:", QLineEdit())
        forms_layout.addRow("العمر:", QSpinBox())
        forms_layout.addRow("المدينة:", QComboBox())
        forms_layout.addRow("ملاحظات:", QTextEdit())
        forms_layout.addRow("", QCheckBox("موافق على الشروط"))
        
        forms_group.setLayout(forms_layout)
        layout.addWidget(forms_group)
        
        # جدول تجريبي
        table_group = QGroupBox("جدول البيانات")
        table_layout = QVBoxLayout()
        
        table = QTableWidget(5, 4)
        table.setHorizontalHeaderLabels(["الاسم", "العمر", "المدينة", "الراتب"])
        
        # ملء الجدول ببيانات تجريبية
        for row in range(5):
            table.setItem(row, 0, QTableWidgetItem(f"شخص {row+1}"))
            table.setItem(row, 1, QTableWidgetItem(f"{25+row}"))
            table.setItem(row, 2, QTableWidgetItem(f"مدينة {row+1}"))
            table.setItem(row, 3, QTableWidgetItem(f"{5000+row*1000}"))
        
        table.horizontalHeader().setStretchLastSection(True)
        table_layout.addWidget(table)
        
        table_group.setLayout(table_layout)
        layout.addWidget(table_group)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        ok_btn = QPushButton("موافق")
        ok_btn.clicked.connect(self.accept)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        
        test_btn = QPushButton("اختبار رسالة")
        test_btn.clicked.connect(self.show_test_message)
        
        buttons_layout.addWidget(ok_btn)
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(test_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
    
    def show_test_message(self):
        """عرض رسالة اختبار"""
        QMessageBox.information(self, "اختبار", "هذه رسالة اختبار للنظام المتجاوب!")


class ResponsiveTestWindow(QMainWindow):
    """نافذة اختبار النظام المتجاوب"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.show_screen_info()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # استخدام النظام المتجاوب
        try:
            from utils.responsive_ui import setup_responsive_window, responsive_manager
            setup_responsive_window(self, 1000, 700, 800, 500)
            self.responsive_available = True
        except ImportError:
            self.setGeometry(100, 100, 1000, 700)
            self.setMinimumSize(800, 500)
            self.responsive_available = False
        
        self.setWindowTitle("🖥️ اختبار النظام المتجاوب")
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # العنوان
        title_label = QLabel("🖥️ اختبار النظام المتجاوب")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-radius: 10px;
                margin: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # معلومات الشاشة
        self.screen_info_group = QGroupBox("📊 معلومات الشاشة")
        self.screen_info_layout = QFormLayout()
        self.screen_info_group.setLayout(self.screen_info_layout)
        layout.addWidget(self.screen_info_group)
        
        # أزرار الاختبار
        buttons_group = QGroupBox("🧪 اختبارات النظام المتجاوب")
        buttons_layout = QVBoxLayout()
        
        # صف الأزرار الأول
        row1_layout = QHBoxLayout()
        
        dialog_btn = QPushButton("🔲 اختبار حوار متجاوب")
        dialog_btn.clicked.connect(self.test_responsive_dialog)
        row1_layout.addWidget(dialog_btn)
        
        message_btn = QPushButton("💬 اختبار رسالة")
        message_btn.clicked.connect(self.test_message_box)
        row1_layout.addWidget(message_btn)
        
        font_btn = QPushButton("🔤 اختبار الخطوط")
        font_btn.clicked.connect(self.test_fonts)
        row1_layout.addWidget(font_btn)
        
        buttons_layout.addLayout(row1_layout)
        
        # صف الأزرار الثاني
        row2_layout = QHBoxLayout()
        
        resize_btn = QPushButton("📏 اختبار تغيير الحجم")
        resize_btn.clicked.connect(self.test_resize)
        row2_layout.addWidget(resize_btn)
        
        fullscreen_btn = QPushButton("🖥️ ملء الشاشة")
        fullscreen_btn.clicked.connect(self.toggle_fullscreen)
        row2_layout.addWidget(fullscreen_btn)
        
        refresh_btn = QPushButton("🔄 تحديث المعلومات")
        refresh_btn.clicked.connect(self.show_screen_info)
        row2_layout.addWidget(refresh_btn)
        
        buttons_layout.addLayout(row2_layout)
        
        buttons_group.setLayout(buttons_layout)
        layout.addWidget(buttons_group)
        
        # منطقة النتائج
        results_group = QGroupBox("📋 نتائج الاختبار")
        results_layout = QVBoxLayout()
        
        self.results_text = QTextEdit()
        self.results_text.setMaximumHeight(200)
        self.results_text.setPlainText("جاهز لبدء الاختبارات...")
        results_layout.addWidget(self.results_text)
        
        results_group.setLayout(results_layout)
        layout.addWidget(results_group)
        
        # تطبيق التنسيق المتجاوب
        if self.responsive_available:
            try:
                from utils.responsive_ui import create_responsive_stylesheet
                self.setStyleSheet(create_responsive_stylesheet(12))
            except:
                pass
    
    def show_screen_info(self):
        """عرض معلومات الشاشة"""
        # مسح المعلومات السابقة
        for i in reversed(range(self.screen_info_layout.count())):
            self.screen_info_layout.itemAt(i).widget().setParent(None)
        
        if self.responsive_available:
            try:
                from utils.responsive_ui import responsive_manager
                
                info = responsive_manager.screen_info
                
                self.screen_info_layout.addRow("الدقة:", QLabel(f"{info['width']} × {info['height']}"))
                self.screen_info_layout.addRow("DPI:", QLabel(f"{info['dpi']:.1f}"))
                self.screen_info_layout.addRow("Device Pixel Ratio:", QLabel(f"{info['device_pixel_ratio']:.2f}"))
                self.screen_info_layout.addRow("فئة الشاشة:", QLabel(responsive_manager.get_screen_category()))
                self.screen_info_layout.addRow("عامل التكبير:", QLabel(f"{responsive_manager.scale_factor:.2f}"))
                self.screen_info_layout.addRow("عامل تكبير الخط:", QLabel(f"{responsive_manager.font_scale:.2f}"))
                
                # إضافة معلومات النافذة الحالية
                current_size = self.size()
                self.screen_info_layout.addRow("حجم النافذة الحالي:", QLabel(f"{current_size.width()} × {current_size.height()}"))
                
                self.log_result("✅ تم تحديث معلومات الشاشة بنجاح")
                
            except Exception as e:
                self.log_result(f"❌ خطأ في الحصول على معلومات الشاشة: {e}")
        else:
            self.screen_info_layout.addRow("الحالة:", QLabel("النظام المتجاوب غير متوفر"))
            self.log_result("⚠️ النظام المتجاوب غير متوفر")
    
    def test_responsive_dialog(self):
        """اختبار الحوار المتجاوب"""
        try:
            dialog = ResponsiveTestDialog(self)
            result = dialog.exec_()
            
            if result == QDialog.Accepted:
                self.log_result("✅ تم اختبار الحوار المتجاوب بنجاح - تم الموافقة")
            else:
                self.log_result("ℹ️ تم اختبار الحوار المتجاوب - تم الإلغاء")
                
        except Exception as e:
            self.log_result(f"❌ خطأ في اختبار الحوار المتجاوب: {e}")
    
    def test_message_box(self):
        """اختبار صندوق الرسائل"""
        try:
            reply = QMessageBox.question(
                self,
                "اختبار النظام المتجاوب",
                "هل يظهر هذا الصندوق بحجم مناسب لشاشتك؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            
            if reply == QMessageBox.Yes:
                self.log_result("✅ صندوق الرسائل يظهر بحجم مناسب")
            else:
                self.log_result("⚠️ صندوق الرسائل لا يظهر بحجم مناسب")
                
        except Exception as e:
            self.log_result(f"❌ خطأ في اختبار صندوق الرسائل: {e}")
    
    def test_fonts(self):
        """اختبار الخطوط المتجاوبة"""
        try:
            if self.responsive_available:
                from utils.responsive_ui import responsive_manager
                
                # اختبار أحجام خطوط مختلفة
                sizes = [10, 12, 14, 16, 18, 20]
                results = []
                
                for size in sizes:
                    scaled_size = responsive_manager.scale_font_size(size)
                    results.append(f"حجم {size} → {scaled_size}")
                
                self.log_result("🔤 نتائج اختبار الخطوط:")
                for result in results:
                    self.log_result(f"   {result}")
            else:
                self.log_result("⚠️ لا يمكن اختبار الخطوط - النظام المتجاوب غير متوفر")
                
        except Exception as e:
            self.log_result(f"❌ خطأ في اختبار الخطوط: {e}")
    
    def test_resize(self):
        """اختبار تغيير حجم النافذة"""
        try:
            current_size = self.size()
            
            # تغيير الحجم إلى حجم مختلف
            new_width = current_size.width() + 100
            new_height = current_size.height() + 50
            
            self.resize(new_width, new_height)
            
            self.log_result(f"📏 تم تغيير حجم النافذة من {current_size.width()}×{current_size.height()} إلى {new_width}×{new_height}")
            
        except Exception as e:
            self.log_result(f"❌ خطأ في تغيير حجم النافذة: {e}")
    
    def toggle_fullscreen(self):
        """تبديل ملء الشاشة"""
        try:
            if self.isFullScreen():
                self.showNormal()
                self.log_result("🖥️ تم الخروج من وضع ملء الشاشة")
            else:
                self.showFullScreen()
                self.log_result("🖥️ تم الدخول في وضع ملء الشاشة")
                
        except Exception as e:
            self.log_result(f"❌ خطأ في تبديل ملء الشاشة: {e}")
    
    def log_result(self, message):
        """تسجيل نتيجة في منطقة النتائج"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.results_text.append(f"[{timestamp}] {message}")


def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تعيين خصائص التطبيق
    app.setApplicationName("اختبار النظام المتجاوب")
    app.setApplicationVersion("1.0")
    
    # إنشاء النافذة الرئيسية
    window = ResponsiveTestWindow()
    window.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
