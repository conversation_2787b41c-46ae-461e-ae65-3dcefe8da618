#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة الويب الاختيارية - عرض التقارير عبر المتصفح
Optional Web Interface - Display reports via browser
"""

from flask import Flask, render_template, jsonify, request, send_file
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from database.models import Transaction, TransactionItem, Product, Customer, Supplier, TransactionType
from utils.performance_optimizer import DatabaseOptimizer, QueryOptimizer
from utils.currency_formatter import format_currency
from datetime import datetime, timedelta
import json
import os
import threading
import webbrowser
from werkzeug.serving import make_server


class WebInterface:
    """واجهة الويب للتقارير"""
    
    def __init__(self, engine, port=5000):
        self.engine = engine
        self.port = port
        self.app = Flask(__name__, 
                        template_folder='templates',
                        static_folder='static')
        self.server = None
        self.db_optimizer = DatabaseOptimizer(engine)
        self.query_optimizer = QueryOptimizer(engine)
        self.setup_routes()
    
    def setup_routes(self):
        """إعداد المسارات"""
        
        @self.app.route('/')
        def dashboard():
            """الصفحة الرئيسية"""
            return render_template('dashboard.html')
        
        @self.app.route('/api/dashboard-data')
        def dashboard_data():
            """بيانات لوحة المعلومات"""
            try:
                with Session(self.engine) as session:
                    today = datetime.now().date()
                    
                    # المبيعات اليوم
                    today_sales = session.query(func.sum(Transaction.total_amount)).filter(
                        Transaction.type == TransactionType.SALE,
                        func.date(Transaction.date) == today
                    ).scalar() or 0
                    
                    # المشتريات اليوم
                    today_purchases = session.query(func.sum(Transaction.total_amount)).filter(
                        Transaction.type == TransactionType.PURCHASE,
                        func.date(Transaction.date) == today
                    ).scalar() or 0
                    
                    # عدد العملاء
                    customers_count = session.query(func.count(Customer.id)).filter(
                        Customer.is_active == True
                    ).scalar() or 0
                    
                    # عدد المنتجات
                    products_count = session.query(func.count(Product.id)).filter(
                        Product.is_active == True
                    ).scalar() or 0
                    
                    return jsonify({
                        'today_sales': float(today_sales),
                        'today_purchases': float(today_purchases),
                        'customers_count': customers_count,
                        'products_count': products_count,
                        'profit': float(today_sales) - float(today_purchases)
                    })
                    
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/sales-chart')
        def sales_chart():
            """بيانات رسم المبيعات"""
            try:
                days = int(request.args.get('days', 7))
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=days)
                
                sales_data = self.db_optimizer.get_optimized_sales_data(start_date, end_date, days)
                
                chart_data = {
                    'labels': [item.sale_date.strftime('%m-%d') for item in sales_data],
                    'data': [float(item.total_sales) for item in sales_data]
                }
                
                return jsonify(chart_data)
                
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/top-products')
        def top_products():
            """أفضل المنتجات مبيعاً"""
            try:
                days = int(request.args.get('days', 30))
                limit = int(request.args.get('limit', 10))
                
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=days)
                
                products_data = self.db_optimizer.get_optimized_top_products(start_date, end_date, limit)
                
                chart_data = {
                    'labels': [item.name for item in products_data],
                    'data': [float(item.total_quantity) for item in products_data],
                    'values': [float(item.total_value) for item in products_data]
                }
                
                return jsonify(chart_data)
                
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/low-stock')
        def low_stock():
            """المنتجات منخفضة المخزون"""
            try:
                limit = int(request.args.get('limit', 20))
                
                low_stock_data = self.db_optimizer.get_optimized_low_stock(limit)
                
                products = []
                for item in low_stock_data:
                    products.append({
                        'name': item.name,
                        'code': item.code,
                        'quantity': item.quantity,
                        'min_quantity': item.min_quantity,
                        'shortage': item.shortage,
                        'category': item.category
                    })
                
                return jsonify(products)
                
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/reports')
        def reports():
            """صفحة التقارير"""
            return render_template('reports.html')
        
        @self.app.route('/api/financial-report')
        def financial_report():
            """التقرير المالي"""
            try:
                start_date = request.args.get('start_date')
                end_date = request.args.get('end_date')
                
                if not start_date or not end_date:
                    end_date = datetime.now().date()
                    start_date = end_date - timedelta(days=30)
                else:
                    start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                    end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                
                aggregated_data = self.query_optimizer.get_aggregated_data(start_date, end_date)
                
                return jsonify(aggregated_data)
                
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/export-excel')
        def export_excel():
            """تصدير إلى Excel"""
            try:
                report_type = request.args.get('type', 'financial')
                start_date = request.args.get('start_date')
                end_date = request.args.get('end_date')
                
                # إنشاء ملف Excel مؤقت
                filename = f"report_{report_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                filepath = os.path.join('temp', filename)
                
                # التأكد من وجود مجلد temp
                os.makedirs('temp', exist_ok=True)
                
                # إنشاء التقرير (يمكن توسيعه لاحقاً)
                # هنا يمكن استخدام نفس منطق التصدير من EnhancedReportsWidget
                
                return send_file(filepath, as_attachment=True, download_name=filename)
                
            except Exception as e:
                return jsonify({'error': str(e)}), 500
    
    def start_server(self, debug=False, open_browser=True):
        """بدء الخادم"""
        try:
            self.server = make_server('127.0.0.1', self.port, self.app, threaded=True)
            
            # بدء الخادم في خيط منفصل
            server_thread = threading.Thread(target=self.server.serve_forever)
            server_thread.daemon = True
            server_thread.start()
            
            print(f"🌐 تم بدء واجهة الويب على: http://127.0.0.1:{self.port}")
            
            # فتح المتصفح
            if open_browser:
                webbrowser.open(f"http://127.0.0.1:{self.port}")
            
            return True
            
        except Exception as e:
            print(f"خطأ في بدء واجهة الويب: {e}")
            return False
    
    def stop_server(self):
        """إيقاف الخادم"""
        if self.server:
            self.server.shutdown()
            print("🛑 تم إيقاف واجهة الويب")


def create_web_templates():
    """إنشاء قوالب HTML"""
    
    # إنشاء مجلد القوالب
    os.makedirs('web/templates', exist_ok=True)
    os.makedirs('web/static/css', exist_ok=True)
    os.makedirs('web/static/js', exist_ok=True)
    
    # قالب لوحة المعلومات
    dashboard_html = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة المعلومات - نظام المحاسبة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">📊 نظام المحاسبة العصري</a>
            <div class="navbar-nav">
                <a class="nav-link" href="/">لوحة المعلومات</a>
                <a class="nav-link" href="/reports">التقارير</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-3 mb-3">
                <div class="card text-white bg-success">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">مبيعات اليوم</h6>
                                <h4 id="today-sales">0</h4>
                            </div>
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card text-white bg-danger">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">مشتريات اليوم</h6>
                                <h4 id="today-purchases">0</h4>
                            </div>
                            <i class="fas fa-shopping-cart fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card text-white bg-info">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">العملاء</h6>
                                <h4 id="customers-count">0</h4>
                            </div>
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card text-white bg-warning">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">المنتجات</h6>
                                <h4 id="products-count">0</h4>
                            </div>
                            <i class="fas fa-box fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>المبيعات آخر 7 أيام</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>أفضل المنتجات</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="productsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
</body>
</html>
    """
    
    with open('web/templates/dashboard.html', 'w', encoding='utf-8') as f:
        f.write(dashboard_html)
    
    # ملف JavaScript للوحة المعلومات
    dashboard_js = """
// تحميل بيانات لوحة المعلومات
async function loadDashboardData() {
    try {
        const response = await fetch('/api/dashboard-data');
        const data = await response.json();
        
        document.getElementById('today-sales').textContent = formatCurrency(data.today_sales);
        document.getElementById('today-purchases').textContent = formatCurrency(data.today_purchases);
        document.getElementById('customers-count').textContent = data.customers_count;
        document.getElementById('products-count').textContent = data.products_count;
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
    }
}

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-EG', {
        style: 'currency',
        currency: 'EGP'
    }).format(amount);
}

// رسم المبيعات
async function loadSalesChart() {
    try {
        const response = await fetch('/api/sales-chart?days=7');
        const data = await response.json();
        
        const ctx = document.getElementById('salesChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'المبيعات',
                    data: data.data,
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
        
    } catch (error) {
        console.error('خطأ في تحميل رسم المبيعات:', error);
    }
}

// رسم أفضل المنتجات
async function loadProductsChart() {
    try {
        const response = await fetch('/api/top-products?days=30&limit=5');
        const data = await response.json();
        
        const ctx = document.getElementById('productsChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.data,
                    backgroundColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0',
                        '#9966FF'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        
    } catch (error) {
        console.error('خطأ في تحميل رسم المنتجات:', error);
    }
}

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
    loadSalesChart();
    loadProductsChart();
    
    // تحديث البيانات كل 5 دقائق
    setInterval(loadDashboardData, 300000);
});
    """
    
    with open('web/static/js/dashboard.js', 'w', encoding='utf-8') as f:
        f.write(dashboard_js)
    
    print("✅ تم إنشاء قوالب واجهة الويب بنجاح")


# مثال على الاستخدام
if __name__ == "__main__":
    # إنشاء القوالب
    create_web_templates()
    
    # بدء واجهة الويب (يحتاج engine من البرنامج الرئيسي)
    # web_interface = WebInterface(engine)
    # web_interface.start_server()
