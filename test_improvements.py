#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التحسينات الجديدة
Test script for new improvements
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class TestWindow(QMainWindow):
    """نافذة اختبار التحسينات"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🧪 اختبار التحسينات الجديدة")
        self.setGeometry(100, 100, 600, 400)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # العنوان
        title = QLabel("🎉 اختبار التحسينات الجديدة")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                padding: 20px;
                border-radius: 10px;
                margin: 10px;
            }
        """)
        layout.addWidget(title)
        
        # أزرار الاختبار
        self.create_test_buttons(layout)
    
    def create_test_buttons(self, layout):
        """إنشاء أزرار الاختبار"""
        
        # اختبار التقارير المحسنة
        reports_btn = QPushButton("📊 اختبار التقارير المحسنة")
        reports_btn.clicked.connect(self.test_enhanced_reports)
        reports_btn.setStyleSheet(self.get_button_style("#28a745"))
        layout.addWidget(reports_btn)
        
        # اختبار واجهة الويب
        web_btn = QPushButton("🌐 اختبار واجهة الويب")
        web_btn.clicked.connect(self.test_web_interface)
        web_btn.setStyleSheet(self.get_button_style("#007bff"))
        layout.addWidget(web_btn)
        
        # اختبار التنبيهات الذكية
        notifications_btn = QPushButton("🔔 اختبار التنبيهات الذكية")
        notifications_btn.clicked.connect(self.test_smart_notifications)
        notifications_btn.setStyleSheet(self.get_button_style("#fd7e14"))
        layout.addWidget(notifications_btn)
        
        # اختبار النسخ الاحتياطي
        backup_btn = QPushButton("💾 اختبار النسخ الاحتياطي المحسن")
        backup_btn.clicked.connect(self.test_enhanced_backup)
        backup_btn.setStyleSheet(self.get_button_style("#6f42c1"))
        layout.addWidget(backup_btn)
        
        # اختبار التحليلات الذكية
        analytics_btn = QPushButton("🧠 اختبار التحليلات الذكية")
        analytics_btn.clicked.connect(self.test_smart_analytics)
        analytics_btn.setStyleSheet(self.get_button_style("#e83e8c"))
        layout.addWidget(analytics_btn)
        
        # اختبار شامل
        full_test_btn = QPushButton("🚀 اختبار شامل لجميع التحسينات")
        full_test_btn.clicked.connect(self.run_full_test)
        full_test_btn.setStyleSheet(self.get_button_style("#dc3545"))
        layout.addWidget(full_test_btn)
    
    def get_button_style(self, color):
        """الحصول على تنسيق الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                padding: 15px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                margin: 5px;
            }}
            QPushButton:hover {{
                background-color: {color}CC;
                transform: translateY(-2px);
            }}
            QPushButton:pressed {{
                background-color: {color}AA;
            }}
        """
    
    def test_enhanced_reports(self):
        """اختبار التقارير المحسنة"""
        try:
            from gui.enhanced_reports import EnhancedReportsWidget
            QMessageBox.information(self, "✅ نجح", "التقارير المحسنة متوفرة وجاهزة للاستخدام!")
        except ImportError as e:
            QMessageBox.warning(self, "⚠️ تحذير", f"التقارير المحسنة غير متوفرة:\n{str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "❌ خطأ", f"خطأ في اختبار التقارير المحسنة:\n{str(e)}")
    
    def test_web_interface(self):
        """اختبار واجهة الويب"""
        try:
            from web.web_interface import WebInterface
            QMessageBox.information(self, "✅ نجح", "واجهة الويب متوفرة وجاهزة للاستخدام!")
        except ImportError as e:
            QMessageBox.warning(self, "⚠️ تحذير", f"واجهة الويب غير متوفرة:\n{str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "❌ خطأ", f"خطأ في اختبار واجهة الويب:\n{str(e)}")
    
    def test_smart_notifications(self):
        """اختبار التنبيهات الذكية"""
        try:
            from utils.smart_notifications import SmartNotificationSystem
            QMessageBox.information(self, "✅ نجح", "التنبيهات الذكية متوفرة وجاهزة للاستخدام!")
        except ImportError as e:
            QMessageBox.warning(self, "⚠️ تحذير", f"التنبيهات الذكية غير متوفرة:\n{str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "❌ خطأ", f"خطأ في اختبار التنبيهات الذكية:\n{str(e)}")
    
    def test_enhanced_backup(self):
        """اختبار النسخ الاحتياطي المحسن"""
        try:
            from utils.enhanced_backup import EnhancedBackupWidget
            QMessageBox.information(self, "✅ نجح", "النسخ الاحتياطي المحسن متوفر وجاهز للاستخدام!")
        except ImportError as e:
            QMessageBox.warning(self, "⚠️ تحذير", f"النسخ الاحتياطي المحسن غير متوفر:\n{str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "❌ خطأ", f"خطأ في اختبار النسخ الاحتياطي المحسن:\n{str(e)}")
    
    def test_smart_analytics(self):
        """اختبار التحليلات الذكية"""
        try:
            from utils.smart_analytics import AnalyticsEngine
            from gui.smart_analytics_widget import SmartAnalyticsWidget
            QMessageBox.information(self, "✅ نجح", "التحليلات الذكية متوفرة وجاهزة للاستخدام!")
        except ImportError as e:
            QMessageBox.warning(self, "⚠️ تحذير", f"التحليلات الذكية غير متوفرة:\n{str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "❌ خطأ", f"خطأ في اختبار التحليلات الذكية:\n{str(e)}")
    
    def run_full_test(self):
        """تشغيل اختبار شامل"""
        results = []
        
        # اختبار كل مكون
        tests = [
            ("التقارير المحسنة", self.test_enhanced_reports),
            ("واجهة الويب", self.test_web_interface),
            ("التنبيهات الذكية", self.test_smart_notifications),
            ("النسخ الاحتياطي المحسن", self.test_enhanced_backup),
            ("التحليلات الذكية", self.test_smart_analytics)
        ]
        
        passed = 0
        failed = 0
        
        for name, test_func in tests:
            try:
                test_func()
                results.append(f"✅ {name}: نجح")
                passed += 1
            except Exception as e:
                results.append(f"❌ {name}: فشل - {str(e)}")
                failed += 1
        
        # عرض النتائج
        summary = f"""
🧪 نتائج الاختبار الشامل:

{chr(10).join(results)}

📊 الملخص:
✅ نجح: {passed}
❌ فشل: {failed}
📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%

{'🎉 جميع التحسينات تعمل بنجاح!' if failed == 0 else '⚠️ بعض التحسينات تحتاج إصلاح'}
        """
        
        QMessageBox.information(self, "📋 نتائج الاختبار الشامل", summary)


def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تطبيق تنسيق عام
    app.setStyleSheet("""
        QMainWindow {
            background-color: #f8f9fa;
        }
        QWidget {
            font-family: 'Segoe UI', Arial, sans-serif;
        }
    """)
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
