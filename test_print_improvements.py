#!/usr/bin/env python3
"""
اختبار تحسينات الطباعة
"""

import os
import sys
from PyQt5.QtWidgets import QApplication
from sqlalchemy import create_engine
from sqlalchemy.orm import Session

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.models import Transaction
from utils.new_design_invoice_printer import NewInvoicePrintDialog

def test_print_improvements():
    """اختبار تحسينات الطباعة"""
    print("🖨️ اختبار تحسينات الطباعة...")
    print("=" * 50)
    
    try:
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        # الاتصال بقاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
        engine = create_engine(f'sqlite:///{db_path}', echo=False)
        print(f"✅ تم الاتصال بقاعدة البيانات: {db_path}")
        
        # البحث عن فاتورة للاختبار
        with Session(engine) as session:
            invoice = session.query(Transaction).first()
            
            if not invoice:
                print("❌ لا توجد فواتير في قاعدة البيانات للاختبار")
                return
            
            print(f"📄 تم العثور على فاتورة للاختبار: {invoice.id}")
            print(f"   📅 التاريخ: {invoice.date}")
            print(f"   💰 المبلغ: {invoice.total_amount}")
        
        # فتح نافذة الطباعة الجديدة
        print("🖨️ فتح نافذة الطباعة...")
        dialog = NewInvoicePrintDialog(engine, invoice.id)
        
        print("✅ تم فتح نافذة الطباعة بنجاح!")
        print("\n📋 التحسينات المطبقة:")
        print("  ✅ للـ A4: الطباعة بنفس حجم وتنسيق العرض")
        print("  ✅ للرول: يبقى بالطريقة العادية")
        print("  ✅ حفظ PDF بنفس جودة العرض")
        print("  ✅ طباعة مباشرة بنفس التنسيق")
        
        print("\n🎯 كيفية الاختبار:")
        print("  1. اختر نوع الطابعة (A4 أو Roll)")
        print("  2. لاحظ المعاينة في النافذة")
        print("  3. جرب 'حفظ كـ PDF' - ستجد نفس التنسيق")
        print("  4. جرب 'طباعة' - ستطبع بنفس الحجم")
        
        # عرض النافذة
        dialog.show()
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الطباعة: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_print_improvements()
