"""
نظام طباعة الفواتير الجديد 2025
يدعم طباعة A4 وطباعة الرول مع تخطيط محسن
"""

import os
from datetime import datetime
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                             QLabel, QMessageBox, QFileDialog, QRadioButton,
                             QButtonGroup, QFrame, QGroupBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog
from PyQt5.QtGui import QTextDocument
from utils.company_settings import get_company_settings


class NewInvoicePrintDialog(QDialog):
    """نافذة طباعة الفواتير الجديدة مع اختيار نوع الطابعة"""

    def __init__(self, engine, invoice_id, parent=None):
        super().__init__(parent)
        self.engine = engine
        self.invoice_id = invoice_id
        self.invoice_data = None
        self.printer_type = "a4"  # افتراضي: A4

        self.setWindowTitle(f"طباعة الفاتورة رقم {invoice_id:06d}")
        self.setMinimumSize(500, 400)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)

        # تحسين النافذة
        self.setStyleSheet("""
            QDialog {
                background-color: #F8F9FA;
                border-radius: 10px;
            }
        """)

        self.setup_ui()
        self.load_invoice_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        self.setLayout(layout)

        # عنوان النافذة
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498DB, stop:1 #2980B9);
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 20px;
            }
        """)
        title_layout = QHBoxLayout()
        title_frame.setLayout(title_layout)

        title_icon = QLabel("🖨️")
        title_icon.setStyleSheet("font-size: 32px; color: white;")

        title_label = QLabel(f"طباعة الفاتورة رقم {self.invoice_id:06d}")
        title_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: white;
            margin-left: 15px;
        """)

        title_layout.addWidget(title_icon)
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        layout.addWidget(title_frame)

        # اختيار نوع الطابعة
        printer_group = QGroupBox("اختر نوع الطابعة")
        printer_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2C3E50;
                border: 2px solid #3498DB;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: #F8F9FA;
            }
        """)
        printer_layout = QVBoxLayout()
        printer_group.setLayout(printer_layout)

        # مجموعة أزرار الاختيار
        self.printer_button_group = QButtonGroup()

        # طابعة A4
        self.a4_radio = QRadioButton("🖨️ طابعة عادية (A4)")
        self.a4_radio.setChecked(True)
        self.a4_radio.setStyleSheet("""
            QRadioButton {
                font-size: 14px;
                font-weight: bold;
                color: #2C3E50;
                padding: 10px;
            }
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        self.a4_radio.toggled.connect(lambda checked: self.set_printer_type("a4") if checked else None)

        # طابعة رول
        self.roll_radio = QRadioButton("📜 طابعة رول (ورق صغير)")
        self.roll_radio.setStyleSheet("""
            QRadioButton {
                font-size: 14px;
                font-weight: bold;
                color: #2C3E50;
                padding: 10px;
            }
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        self.roll_radio.toggled.connect(lambda checked: self.set_printer_type("roll") if checked else None)

        self.printer_button_group.addButton(self.a4_radio)
        self.printer_button_group.addButton(self.roll_radio)

        printer_layout.addWidget(self.a4_radio)
        printer_layout.addWidget(self.roll_radio)

        layout.addWidget(printer_group)

        # أزرار الطباعة
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #E9ECEF;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        buttons_layout = QVBoxLayout()
        buttons_layout.setSpacing(15)
        buttons_frame.setLayout(buttons_layout)

        # زر معاينة قبل الطباعة
        preview_btn = QPushButton("👁️ معاينة قبل الطباعة")
        preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 15px 25px;
                border: none;
                border-radius: 8px;
                min-height: 50px;
                text-align: left;
            }
            QPushButton:hover {
                background-color: #2980B9;
                transform: translateY(-2px);
            }
        """)
        preview_btn.clicked.connect(self.print_preview)
        buttons_layout.addWidget(preview_btn)

        # زر حفظ كـ PDF
        save_pdf_btn = QPushButton("💾 حفظ كملف PDF")
        save_pdf_btn.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 15px 25px;
                border: none;
                border-radius: 8px;
                min-height: 50px;
                text-align: left;
            }
            QPushButton:hover {
                background-color: #C0392B;
                transform: translateY(-2px);
            }
        """)
        save_pdf_btn.clicked.connect(self.save_as_pdf)
        buttons_layout.addWidget(save_pdf_btn)

        # زر طباعة مباشرة
        print_btn = QPushButton("🖨️ طباعة مباشرة")
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 15px 25px;
                border: none;
                border-radius: 8px;
                min-height: 50px;
                text-align: left;
            }
            QPushButton:hover {
                background-color: #229954;
                transform: translateY(-2px);
            }
        """)
        print_btn.clicked.connect(self.print_direct)
        buttons_layout.addWidget(print_btn)

        layout.addWidget(buttons_frame)

        # أزرار التحكم
        control_layout = QHBoxLayout()

        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95A5A6;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #7F8C8D;
            }
        """)
        cancel_btn.clicked.connect(self.reject)

        control_layout.addStretch()
        control_layout.addWidget(cancel_btn)

        layout.addLayout(control_layout)

    def set_printer_type(self, printer_type):
        """تحديد نوع الطابعة"""
        self.printer_type = printer_type
    
    def load_invoice_data(self):
        """تحميل بيانات الفاتورة"""
        try:
            from sqlalchemy.orm import sessionmaker
            Session = sessionmaker(bind=self.engine)
            session = Session()

            # استعلام الفاتورة
            from database.models import Transaction, Customer, TransactionItem, Product

            transaction = session.query(Transaction).filter_by(id=self.invoice_id).first()
            if not transaction:
                raise Exception("الفاتورة غير موجودة")

            # بيانات العميل
            customer = session.query(Customer).filter_by(id=transaction.customer_id).first() if transaction.customer_id else None

            # عناصر الفاتورة
            items = session.query(TransactionItem).filter_by(transaction_id=self.invoice_id).all()
            invoice_items = []

            for item in items:
                product = session.query(Product).filter_by(id=item.product_id).first()
                if product:
                    invoice_items.append({
                        'name': product.name,
                        'quantity': item.quantity,
                        'unit': product.unit,
                        'price': float(item.price),
                        'total': float(item.quantity * item.price)
                    })

            # تجميع البيانات
            self.invoice_data = {
                'id': transaction.id,
                'date': transaction.date.strftime('%Y-%m-%d %H:%M') if transaction.date else "",
                'customer_name': customer.name if customer else "عميل نقدي",
                'customer_phone': customer.phone if customer else "",
                'customer_address': customer.address if customer else "",
                'items': invoice_items,
                'total_amount': float(transaction.total_amount or 0),
                'paid_amount': float(transaction.paid_amount or 0),
                'discount': float(transaction.discount or 0),
                'remaining_amount': float((transaction.total_amount or 0) - (transaction.paid_amount or 0))
            }

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الفاتورة:\n{str(e)}")
    
    def generate_invoice_html(self):
        """إنشاء HTML للفاتورة حسب نوع الطابعة المختار"""
        if not self.invoice_data:
            return ""

        if self.printer_type == "a4":
            return self.generate_a4_invoice_html()
        else:
            return self.generate_roll_invoice_html()

    def generate_a4_invoice_html(self):
        """إنشاء HTML للفاتورة بتخطيط A4 المحسن"""
        if not self.invoice_data:
            return ""

        # تحميل إعدادات الشركة
        company_settings = get_company_settings()

        # معلومات الشركة
        company_name = company_settings.get("company_name", "اسم الشركة")
        company_address = company_settings.get("address", "")
        company_phone = company_settings.get("phone", "")
        company_email = company_settings.get("email", "")
        company_tax_number = company_settings.get("tax_number", "")
        company_commercial_register = company_settings.get("commercial_register", "")

        # الشعار
        logo_html = ""
        if company_settings.get("logo_base64"):
            logo_html = f'<img src="data:image/png;base64,{company_settings["logo_base64"]}" class="logo">'

        # حساب المجموع الفرعي
        subtotal = sum(item['total'] for item in self.invoice_data['items'])

        # تقسيم المنتجات إذا كانت كثيرة
        items_per_page = 8  # عدد المنتجات في الصفحة الأولى
        first_page_items = self.invoice_data['items'][:items_per_page]
        remaining_items = self.invoice_data['items'][items_per_page:]

        html = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة رقم {self.invoice_data['id']:06d}</title>
            <style>
                @page {{
                    size: A4;
                    margin: 15mm;
                }}

                * {{
                    box-sizing: border-box;
                }}

                body {{
                    font-family: 'Arial', 'Tahoma', sans-serif;
                    font-size: 24px;
                    line-height: 1.6;
                    color: #000;
                    margin: 0;
                    padding: 0;
                    width: 100%;
                    height: 100vh;
                    direction: rtl;
                    font-weight: bold;
                }}

                .invoice-container {{
                    width: 100%;
                    height: 100vh;
                    display: flex;
                    flex-direction: column;
                    border: 3px solid #000;
                }}

                /* الربع الأول - معلومات الشركة واللوجو */
                .quarter-1 {{
                    height: 25%;
                    border-bottom: 3px solid #000;
                    padding: 20px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    background-color: #f9f9f9;
                }}

                .company-info {{
                    flex: 1;
                    text-align: right;
                    padding-left: 20px;
                }}

                .company-name {{
                    font-size: 36px;
                    font-weight: bold;
                    margin-bottom: 12px;
                    color: #000;
                    line-height: 1.3;
                }}

                .company-details {{
                    font-size: 24px;
                    line-height: 1.8;
                    color: #333;
                    font-weight: bold;
                }}

                .logo-container {{
                    flex: 1;
                    text-align: center;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding-right: 20px;
                }}

                .logo {{
                    max-width: 150px;
                    max-height: 150px;
                    object-fit: contain;
                }}

                /* الربع الثاني - معلومات العميل */
                .quarter-2 {{
                    height: 25%;
                    border-bottom: 3px solid #000;
                    padding: 20px;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    background-color: #fff;
                }}

                .customer-title {{
                    font-size: 32px;
                    font-weight: bold;
                    margin-bottom: 18px;
                    text-align: center;
                    border-bottom: 4px solid #000;
                    padding-bottom: 12px;
                    color: #000;
                }}

                .customer-info {{
                    font-size: 26px;
                    line-height: 2.0;
                    color: #000;
                    font-weight: bold;
                }}

                /* الربع الثالث - تفاصيل المنتجات */
                .quarter-3 {{
                    flex: 1;
                    border-bottom: 3px solid #000;
                    padding: 15px;
                    display: flex;
                    flex-direction: column;
                    background-color: #fff;
                }}

                .products-title {{
                    font-size: 32px;
                    font-weight: bold;
                    text-align: center;
                    margin-bottom: 18px;
                    border-bottom: 4px solid #000;
                    padding-bottom: 12px;
                    color: #000;
                }}

                .products-table {{
                    width: 100%;
                    border-collapse: collapse;
                    font-size: 22px;
                    flex: 1;
                }}

                .products-table th {{
                    background-color: #000;
                    color: white;
                    padding: 16px;
                    text-align: center;
                    font-weight: bold;
                    font-size: 26px;
                    border: 3px solid #000;
                }}

                .products-table td {{
                    padding: 14px;
                    text-align: center;
                    border: 3px solid #000;
                    font-size: 22px;
                    font-weight: bold;
                }}

                .product-name {{
                    text-align: right !important;
                    font-weight: bold;
                    padding-right: 20px !important;
                    font-size: 24px !important;
                }}

                /* الربع الرابع - تفاصيل الأسعار */
                .quarter-4 {{
                    height: 25%;
                    padding: 20px;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    background-color: #f9f9f9;
                }}

                .totals-title {{
                    font-size: 32px;
                    font-weight: bold;
                    text-align: center;
                    margin-bottom: 18px;
                    border-bottom: 4px solid #000;
                    padding-bottom: 12px;
                    color: #000;
                }}

                .totals-table {{
                    width: 100%;
                    font-size: 24px;
                    border-collapse: collapse;
                }}

                .totals-table td {{
                    padding: 16px;
                    border: 4px solid #000;
                    font-weight: bold;
                }}

                .totals-table .label {{
                    text-align: right;
                    background-color: #e0e0e0;
                    width: 50%;
                    font-size: 24px;
                    font-weight: bold;
                }}

                .totals-table .value {{
                    text-align: center;
                    width: 50%;
                    font-size: 24px;
                    font-weight: bold;
                }}

                .final-total {{
                    background-color: #000 !important;
                    color: white !important;
                    font-size: 28px !important;
                    font-weight: bold !important;
                }}

                /* صفحة إضافية للمنتجات */
                .additional-page {{
                    width: 100%;
                    height: 100vh;
                    display: flex;
                    flex-direction: column;
                    padding: 30px;
                    page-break-before: always;
                }}

                .additional-title {{
                    font-size: 36px;
                    font-weight: bold;
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 5px solid #000;
                    padding-bottom: 18px;
                    color: #000;
                }}

                @media print {{
                    body {{
                        font-size: 22px;
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                    }}

                    .quarter-1, .quarter-2, .quarter-3, .quarter-4 {{
                        page-break-inside: avoid;
                    }}

                    .invoice-container {{
                        page-break-inside: avoid;
                    }}

                    .products-table th {{
                        font-size: 24px !important;
                    }}

                    .products-table td {{
                        font-size: 20px !important;
                    }}

                    .product-name {{
                        font-size: 22px !important;
                    }}

                    .totals-table td {{
                        font-size: 22px !important;
                    }}

                    .final-total {{
                        font-size: 26px !important;
                    }}
                }}
            </style>
        </head>
        <body>
            <!-- الفاتورة الرئيسية -->
            <div class="invoice-container">
                <!-- الربع الأول: معلومات الشركة واللوجو -->
                <div class="quarter-1">
                    <div class="company-info">
                        <div class="company-name">{company_name}</div>
                        <div class="company-details">
                            {f'📍 {company_address}<br>' if company_address else ''}
                            {f'📞 {company_phone}<br>' if company_phone else ''}
                            {f'📧 {company_email}<br>' if company_email else ''}
                            {f'🏢 ض.ب: {company_tax_number}<br>' if company_tax_number else ''}
                            {f'📄 س.ج: {company_commercial_register}' if company_commercial_register else ''}
                        </div>
                    </div>
                    <div class="logo-container">
                        {logo_html if logo_html else '<div style="font-size: 60px; color: #666;">🏢</div>'}
                    </div>
                </div>

                <!-- الربع الثاني: معلومات العميل -->
                <div class="quarter-2">
                    <div class="customer-title">بيانات العميل - فاتورة رقم {self.invoice_data['id']:06d}</div>
                    <div class="customer-info">
                        <strong>👤 اسم العميل:</strong> {self.invoice_data['customer_name']}<br>
                        {f"<strong>📞 الهاتف:</strong> {self.invoice_data['customer_phone']}<br>" if self.invoice_data['customer_phone'] else ''}
                        {f"<strong>📍 العنوان:</strong> {self.invoice_data['customer_address']}<br>" if self.invoice_data['customer_address'] else ''}
                        <strong>📅 التاريخ:</strong> {self.invoice_data['date']}
                    </div>
                </div>

                <!-- الربع الثالث: تفاصيل المنتجات -->
                <div class="quarter-3">
                    <div class="products-title">تفاصيل المنتجات</div>
                    <table class="products-table">
                        <thead>
                            <tr>
                                <th style="width: 15%;">الإجمالي</th>
                                <th style="width: 15%;">الوحدة</th>
                                <th style="width: 15%;">السعر</th>
                                <th style="width: 15%;">الكمية</th>
                                <th style="width: 40%;">المنتج</th>
                            </tr>
                        </thead>
                        <tbody>
        """

        # إضافة منتجات الصفحة الأولى
        for item in first_page_items:
            html += f"""
                            <tr>
                                <td><strong>{item['total']:,.0f}</strong></td>
                                <td>{item['unit']}</td>
                                <td>{item['price']:,.0f}</td>
                                <td>{item['quantity']}</td>
                                <td class="product-name">{item['name']}</td>
                            </tr>
            """

        html += f"""
                        </tbody>
                    </table>
                </div>

                <!-- الربع الرابع: تفاصيل الأسعار -->
                <div class="quarter-4">
                    <div class="totals-title">ملخص الفاتورة</div>
                    <table class="totals-table">
                        <tr>
                            <td class="label">المجموع الفرعي:</td>
                            <td class="value">{subtotal:,.0f} ج.م</td>
                        </tr>
                        {f'<tr><td class="label">الخصم:</td><td class="value">{self.invoice_data["discount"]:,.0f} ج.م</td></tr>' if self.invoice_data['discount'] > 0 else ''}
                        <tr class="final-total">
                            <td class="label final-total">الإجمالي النهائي:</td>
                            <td class="value final-total">{self.invoice_data['total_amount']:,.0f} ج.م</td>
                        </tr>
                        <tr>
                            <td class="label">المدفوع:</td>
                            <td class="value">{self.invoice_data['paid_amount']:,.0f} ج.م</td>
                        </tr>
                        {f'<tr><td class="label">المتبقي:</td><td class="value">{self.invoice_data["remaining_amount"]:,.0f} ج.م</td></tr>' if self.invoice_data['remaining_amount'] > 0 else ''}
                    </table>
                </div>
            </div>
        """

        # إضافة صفحة إضافية للمنتجات المتبقية إذا وجدت
        if remaining_items:
            html += f"""
            <!-- صفحة إضافية للمنتجات المتبقية -->
            <div class="additional-page">
                <div class="additional-title">تكملة منتجات الفاتورة رقم {self.invoice_data['id']:06d}</div>
                <table class="products-table" style="width: 100%; border: 3px solid #000;">
                    <thead>
                        <tr>
                            <th style="width: 15%;">الإجمالي</th>
                            <th style="width: 15%;">الوحدة</th>
                            <th style="width: 15%;">السعر</th>
                            <th style="width: 15%;">الكمية</th>
                            <th style="width: 40%;">المنتج</th>
                        </tr>
                    </thead>
                    <tbody>
            """

            for item in remaining_items:
                html += f"""
                        <tr>
                            <td><strong>{item['total']:,.0f}</strong></td>
                            <td>{item['unit']}</td>
                            <td>{item['price']:,.0f}</td>
                            <td>{item['quantity']}</td>
                            <td class="product-name">{item['name']}</td>
                        </tr>
                """

            html += """
                    </tbody>
                </table>
            </div>
            """

        html += """
        </body>
        </html>
        """

        return html

    def generate_roll_invoice_html(self):
        """إنشاء HTML للفاتورة بتخطيط الرول (ورق صغير)"""
        if not self.invoice_data:
            return ""

        # تحميل إعدادات الشركة
        company_settings = get_company_settings()

        # معلومات الشركة
        company_name = company_settings.get("company_name", "اسم الشركة")
        company_address = company_settings.get("address", "")
        company_phone = company_settings.get("phone", "")
        company_email = company_settings.get("email", "")
        company_tax_number = company_settings.get("tax_number", "")
        company_commercial_register = company_settings.get("commercial_register", "")

        # الشعار
        logo_html = ""
        if company_settings.get("logo_base64"):
            logo_html = f'<img src="data:image/png;base64,{company_settings["logo_base64"]}" class="logo">'

        # حساب المجموع الفرعي
        subtotal = sum(item['total'] for item in self.invoice_data['items'])

        html = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة رقم {self.invoice_data['id']:06d}</title>
            <style>
                @page {{
                    size: 80mm auto;
                    margin: 5mm;
                }}

                body {{
                    font-family: 'Arial', 'Tahoma', sans-serif;
                    font-size: 18px;
                    line-height: 1.6;
                    color: #000;
                    margin: 0;
                    padding: 0;
                    width: 70mm;
                    direction: rtl;
                    font-weight: bold;
                }}

                .header {{
                    text-align: center;
                    border-bottom: 2px solid #000;
                    padding-bottom: 10px;
                    margin-bottom: 10px;
                }}

                .company-name {{
                    font-size: 22px;
                    font-weight: bold;
                    margin-bottom: 10px;
                    color: #000;
                }}

                .company-details {{
                    font-size: 16px;
                    line-height: 1.6;
                    color: #333;
                    font-weight: bold;
                }}

                .logo {{
                    max-width: 60px;
                    max-height: 60px;
                    object-fit: contain;
                    margin-bottom: 10px;
                }}

                .invoice-title {{
                    text-align: center;
                    font-size: 20px;
                    font-weight: bold;
                    margin: 15px 0;
                    border: 3px solid #000;
                    padding: 12px;
                    background-color: #f0f0f0;
                }}

                .customer-info {{
                    margin-bottom: 15px;
                    font-size: 17px;
                    border-bottom: 3px dashed #000;
                    padding-bottom: 12px;
                    line-height: 1.8;
                    font-weight: bold;
                }}

                .products-table {{
                    width: 100%;
                    border-collapse: collapse;
                    font-size: 16px;
                    margin-bottom: 15px;
                    border: 3px solid #000;
                }}

                .products-table th {{
                    background-color: #000;
                    color: white;
                    padding: 10px;
                    text-align: center;
                    font-size: 16px;
                    font-weight: bold;
                    border: 2px solid #000;
                }}

                .products-table td {{
                    padding: 8px;
                    text-align: center;
                    border: 2px solid #000;
                    font-size: 16px;
                    font-weight: bold;
                }}

                .product-name {{
                    text-align: right !important;
                    font-weight: bold;
                    padding-right: 12px !important;
                    font-size: 17px !important;
                }}

                .totals {{
                    border-top: 4px solid #000;
                    padding-top: 12px;
                    font-size: 17px;
                }}

                .total-row {{
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 6px;
                    padding: 6px 0;
                    font-weight: bold;
                }}

                .final-total {{
                    font-weight: bold;
                    font-size: 18px;
                    border-top: 3px solid #000;
                    border-bottom: 3px solid #000;
                    padding: 10px 0;
                    margin: 10px 0;
                    background-color: #f0f0f0;
                }}

                .footer {{
                    text-align: center;
                    margin-top: 15px;
                    font-size: 16px;
                    border-top: 3px dashed #000;
                    padding-top: 10px;
                    font-weight: bold;
                }}

                @media print {{
                    body {{
                        font-size: 17px;
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                    }}
                }}
            </style>
        </head>
        <body>
            <!-- الترويسة -->
            <div class="header">
                {logo_html if logo_html else ''}
                <div class="company-name">{company_name}</div>
                <div class="company-details">
                    {f'{company_address}<br>' if company_address else ''}
                    {f'هاتف: {company_phone}<br>' if company_phone else ''}
                    {f'{company_email}<br>' if company_email else ''}
                    {f'ض.ب: {company_tax_number}<br>' if company_tax_number else ''}
                    {f'س.ج: {company_commercial_register}' if company_commercial_register else ''}
                </div>
            </div>

            <!-- عنوان الفاتورة -->
            <div class="invoice-title">
                فاتورة رقم {self.invoice_data['id']:06d}
            </div>

            <!-- معلومات العميل -->
            <div class="customer-info">
                <strong>العميل:</strong> {self.invoice_data['customer_name']}<br>
                {f"<strong>الهاتف:</strong> {self.invoice_data['customer_phone']}<br>" if self.invoice_data['customer_phone'] else ''}
                <strong>التاريخ:</strong> {self.invoice_data['date']}
            </div>

            <!-- جدول المنتجات -->
            <table class="products-table">
                <thead>
                    <tr>
                        <th style="width: 15%;">الإجمالي</th>
                        <th style="width: 15%;">الوحدة</th>
                        <th style="width: 15%;">السعر</th>
                        <th style="width: 15%;">الكمية</th>
                        <th style="width: 40%;">المنتج</th>
                    </tr>
                </thead>
                <tbody>
        """

        # إضافة عناصر الفاتورة
        for item in self.invoice_data['items']:
            html += f"""
                    <tr>
                        <td><strong>{item['total']:,.0f}</strong></td>
                        <td>{item['unit']}</td>
                        <td>{item['price']:,.0f}</td>
                        <td>{item['quantity']}</td>
                        <td class="product-name">{item['name']}</td>
                    </tr>
            """

        html += f"""
                </tbody>
            </table>

            <!-- المجاميع -->
            <div class="totals">
                <div class="total-row">
                    <span>المجموع الفرعي:</span>
                    <span>{subtotal:,.0f} ج.م</span>
                </div>
                {f'<div class="total-row"><span>الخصم:</span><span>{self.invoice_data["discount"]:,.0f} ج.م</span></div>' if self.invoice_data['discount'] > 0 else ''}
                <div class="total-row final-total">
                    <span>الإجمالي النهائي:</span>
                    <span>{self.invoice_data['total_amount']:,.0f} ج.م</span>
                </div>
                <div class="total-row">
                    <span>المدفوع:</span>
                    <span>{self.invoice_data['paid_amount']:,.0f} ج.م</span>
                </div>
                {f'<div class="total-row"><span>المتبقي:</span><span>{self.invoice_data["remaining_amount"]:,.0f} ج.م</span></div>' if self.invoice_data['remaining_amount'] > 0 else ''}
            </div>

            <!-- التذييل -->
            <div class="footer">
                <div style="font-weight: bold; margin-bottom: 5px;">شكراً لتعاملكم معنا</div>
                <div>تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M')}</div>
            </div>
        </body>
        </html>
        """

        return html

    def print_preview(self):
        """معاينة الفاتورة قبل الطباعة"""
        try:
            html = self.generate_invoice_html()
            if not html:
                QMessageBox.warning(self, "تحذير", "لا توجد بيانات للطباعة")
                return

            # إنشاء مستند للطباعة
            document = QTextDocument()
            document.setHtml(html)

            # إعداد الطابعة حسب النوع
            printer = QPrinter(QPrinter.HighResolution)
            if self.printer_type == "a4":
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(10, 10, 10, 10, QPrinter.Millimeter)
            else:  # roll
                printer.setPageSize(QPrinter.Custom)
                printer.setPaperSize(80, 200, QPrinter.Millimeter)  # عرض 80مم، طول متغير
                printer.setPageMargins(5, 5, 5, 5, QPrinter.Millimeter)

            # نافذة المعاينة
            preview_dialog = QPrintPreviewDialog(printer, self)
            preview_dialog.setWindowTitle(f"معاينة الفاتورة - {self.printer_type.upper()}")
            preview_dialog.paintRequested.connect(lambda: document.print_(printer))
            preview_dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء المعاينة:\n{str(e)}")

    def save_as_pdf(self):
        """حفظ الفاتورة كملف PDF"""
        try:
            html = self.generate_invoice_html()
            if not html:
                QMessageBox.warning(self, "تحذير", "لا توجد بيانات للحفظ")
                return

            # اختيار مكان الحفظ
            printer_type_name = "A4" if self.printer_type == "a4" else "رول"
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ الفاتورة",
                f"فاتورة_{printer_type_name}_{self.invoice_data['id']:06d}_{datetime.now().strftime('%Y%m%d')}.pdf",
                "PDF Files (*.pdf)"
            )

            if filename:
                # إنشاء مستند للطباعة
                document = QTextDocument()
                document.setHtml(html)

                # إعداد الطابعة للـ PDF حسب النوع
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(filename)

                if self.printer_type == "a4":
                    printer.setPageSize(QPrinter.A4)
                    printer.setPageMargins(10, 10, 10, 10, QPrinter.Millimeter)

                    # للـ A4: طباعة بحجم العرض الحالي
                    self.print_with_exact_size(document, printer)
                else:  # roll
                    printer.setPageSize(QPrinter.Custom)
                    printer.setPaperSize(80, 200, QPrinter.Millimeter)
                    printer.setPageMargins(5, 5, 5, 5, QPrinter.Millimeter)

                    # للرول: طباعة عادية
                    document.print_(printer)

                QMessageBox.information(self, "نجح", f"تم حفظ الفاتورة بنجاح:\n{filename}")

                # سؤال المستخدم إذا كان يريد فتح الملف
                reply = QMessageBox.question(
                    self,
                    "فتح الملف",
                    "هل تريد فتح الملف المحفوظ؟",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    os.startfile(filename)  # Windows

                self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحفظ:\n{str(e)}")

    def print_direct(self):
        """طباعة مباشرة"""
        try:
            html = self.generate_invoice_html()
            if not html:
                QMessageBox.warning(self, "تحذير", "لا توجد بيانات للطباعة")
                return

            # إنشاء مستند للطباعة
            document = QTextDocument()
            document.setHtml(html)

            # إعداد الطابعة حسب النوع
            printer = QPrinter(QPrinter.HighResolution)
            if self.printer_type == "a4":
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(10, 10, 10, 10, QPrinter.Millimeter)
            else:  # roll
                printer.setPageSize(QPrinter.Custom)
                printer.setPaperSize(80, 200, QPrinter.Millimeter)
                printer.setPageMargins(5, 5, 5, 5, QPrinter.Millimeter)

            # نافذة اختيار الطابعة
            print_dialog = QPrintDialog(printer, self)
            print_dialog.setWindowTitle(f"طباعة الفاتورة - {self.printer_type.upper()}")

            if print_dialog.exec_() == QPrintDialog.Accepted:
                if self.printer_type == "a4":
                    # للـ A4: طباعة بحجم العرض الحالي
                    self.print_with_exact_size(document, printer)
                else:
                    # للرول: طباعة عادية
                    document.print_(printer)
                QMessageBox.information(self, "نجح", "تم إرسال الفاتورة للطباعة بنجاح")
                self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الطباعة:\n{str(e)}")

    def print_with_exact_size(self, document, printer):
        """طباعة بحجم العرض الحالي للـ A4"""
        try:
            # تعيين حجم الصفحة بناءً على حجم المحتوى
            document.setPageSize(printer.pageRect().size())

            # طباعة المستند
            document.print_(printer)

        except Exception as e:
            print(f"خطأ في الطباعة بحجم العرض: {e}")
            # في حالة الخطأ، استخدم الطريقة العادية
            document.print_(printer)


def show_new_print_dialog(engine, invoice_id, parent=None):
    """عرض نافذة الطباعة الجديدة"""
    dialog = NewInvoicePrintDialog(engine, invoice_id, parent)
    return dialog.exec_()

# للتوافق مع الكود القديم
def show_quarter_print_dialog(engine, invoice_id, parent=None):
    """عرض نافذة الطباعة الجديدة (للتوافق مع الكود القديم)"""
    return show_new_print_dialog(engine, invoice_id, parent)
