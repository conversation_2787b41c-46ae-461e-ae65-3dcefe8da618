#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التنبيهات الذكي - تنبيهات تلقائية للأحداث المهمة
Smart Notifications System - Automatic alerts for important events
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QFrame, QScrollArea, QMessageBox,
                             QSystemTrayIcon, QMenu, QAction)
from PyQt5.QtCore import QTimer, QThread, pyqtSignal, Qt, QDateTime
from PyQt5.QtGui import QIcon, QPixmap, QColor, QFont
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, desc
from database.models import Transaction, Product, Customer, Supplier, TransactionType
from utils.currency_formatter import format_currency
from datetime import datetime, timedelta
import json
import os


class NotificationChecker(QThread):
    """فاحص التنبيهات في خيط منفصل"""
    
    notification_found = pyqtSignal(dict)
    
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.running = True
        
    def run(self):
        """تشغيل فحص التنبيهات"""
        while self.running:
            try:
                notifications = self.check_all_notifications()
                for notification in notifications:
                    self.notification_found.emit(notification)
                
                # فحص كل 5 دقائق
                self.msleep(300000)
                
            except Exception as e:
                print(f"خطأ في فحص التنبيهات: {e}")
                self.msleep(60000)  # إعادة المحاولة بعد دقيقة
    
    def stop(self):
        """إيقاف الفحص"""
        self.running = False
    
    def check_all_notifications(self):
        """فحص جميع أنواع التنبيهات"""
        notifications = []
        
        with Session(self.engine) as session:
            # فحص المخزون المنخفض
            low_stock = self.check_low_stock(session)
            notifications.extend(low_stock)
            
            # فحص المبيعات الاستثنائية
            exceptional_sales = self.check_exceptional_sales(session)
            notifications.extend(exceptional_sales)
            
            # فحص العملاء المتأخرين
            overdue_customers = self.check_overdue_customers(session)
            notifications.extend(overdue_customers)
            
            # فحص الأهداف الشهرية
            monthly_targets = self.check_monthly_targets(session)
            notifications.extend(monthly_targets)
            
        return notifications
    
    def check_low_stock(self, session):
        """فحص المنتجات منخفضة المخزون"""
        notifications = []
        
        # المنتجات التي وصلت للحد الأدنى
        critical_products = session.query(Product).filter(
            Product.quantity <= Product.min_quantity,
            Product.is_active == True
        ).all()
        
        for product in critical_products:
            if product.quantity == 0:
                priority = "عالي"
                message = f"⚠️ نفد مخزون المنتج: {product.name}"
                color = "#dc3545"
            elif product.quantity <= product.min_quantity * 0.5:
                priority = "متوسط"
                message = f"📉 مخزون منخفض جداً: {product.name} (متبقي: {product.quantity})"
                color = "#fd7e14"
            else:
                priority = "منخفض"
                message = f"📊 مخزون منخفض: {product.name} (متبقي: {product.quantity})"
                color = "#ffc107"
            
            notifications.append({
                'type': 'مخزون',
                'priority': priority,
                'message': message,
                'details': f"الحد الأدنى: {product.min_quantity}",
                'color': color,
                'timestamp': datetime.now(),
                'action': 'view_product',
                'action_data': {'product_id': product.id}
            })
        
        return notifications
    
    def check_exceptional_sales(self, session):
        """فحص المبيعات الاستثنائية"""
        notifications = []
        today = datetime.now().date()
        
        # مبيعات اليوم
        today_sales = session.query(func.sum(Transaction.total_amount)).filter(
            Transaction.type == 'بيع',
            func.date(Transaction.date) == today
        ).scalar() or 0
        
        # متوسط المبيعات آخر 30 يوم
        month_ago = today - timedelta(days=30)
        avg_sales = session.query(func.avg(
            session.query(func.sum(Transaction.total_amount)).filter(
                Transaction.type == 'بيع',
                func.date(Transaction.date) >= month_ago,
                func.date(Transaction.date) < today
            ).group_by(func.date(Transaction.date)).subquery().c.total_amount
        )).scalar() or 0
        
        # تحقق من المبيعات الاستثنائية
        if today_sales > avg_sales * 1.5:  # 150% من المتوسط
            notifications.append({
                'type': 'مبيعات',
                'priority': 'عالي',
                'message': f"🎉 مبيعات استثنائية اليوم: {format_currency(today_sales)}",
                'details': f"أعلى من المتوسط بـ {((today_sales/avg_sales-1)*100):.0f}%",
                'color': "#28a745",
                'timestamp': datetime.now(),
                'action': 'view_sales_report',
                'action_data': {'date': today}
            })
        elif today_sales < avg_sales * 0.5:  # أقل من 50% من المتوسط
            notifications.append({
                'type': 'مبيعات',
                'priority': 'متوسط',
                'message': f"📉 مبيعات منخفضة اليوم: {format_currency(today_sales)}",
                'details': f"أقل من المتوسط بـ {((1-today_sales/avg_sales)*100):.0f}%",
                'color': "#ffc107",
                'timestamp': datetime.now(),
                'action': 'view_sales_report',
                'action_data': {'date': today}
            })
        
        return notifications
    
    def check_overdue_customers(self, session):
        """فحص العملاء المتأخرين في السداد"""
        notifications = []
        
        # هذا مثال - يمكن تطويره حسب نظام الائتمان
        # العملاء الذين لديهم معاملات غير مسددة منذ أكثر من 30 يوم
        month_ago = datetime.now().date() - timedelta(days=30)
        
        overdue_transactions = session.query(
            Customer.name,
            func.sum(Transaction.total_amount).label('total_amount'),
            func.count(Transaction.id).label('transaction_count')
        ).join(Transaction).filter(
            Transaction.type == 'بيع',
            Transaction.date <= month_ago,
            # يمكن إضافة شرط حالة السداد هنا
        ).group_by(Customer.id).having(
            func.sum(Transaction.total_amount) > 1000  # أكثر من 1000 جنيه
        ).all()
        
        for customer_data in overdue_transactions:
            notifications.append({
                'type': 'عملاء',
                'priority': 'متوسط',
                'message': f"💳 عميل متأخر في السداد: {customer_data.name}",
                'details': f"المبلغ: {format_currency(customer_data.total_amount)}",
                'color': "#dc3545",
                'timestamp': datetime.now(),
                'action': 'view_customer',
                'action_data': {'customer_name': customer_data.name}
            })
        
        return notifications
    
    def check_monthly_targets(self, session):
        """فحص الأهداف الشهرية"""
        notifications = []
        
        # بداية الشهر الحالي
        today = datetime.now().date()
        month_start = today.replace(day=1)
        
        # مبيعات الشهر الحالي
        monthly_sales = session.query(func.sum(Transaction.total_amount)).filter(
            Transaction.type == 'بيع',
            Transaction.date >= month_start
        ).scalar() or 0
        
        # هدف شهري افتراضي (يمكن جعله قابل للتخصيص)
        monthly_target = 100000  # 100,000 جنيه
        
        # نسبة الإنجاز
        progress = (monthly_sales / monthly_target) * 100 if monthly_target > 0 else 0
        days_passed = (today - month_start).days + 1
        days_in_month = 30  # تقريبي
        expected_progress = (days_passed / days_in_month) * 100
        
        if progress >= 100:
            notifications.append({
                'type': 'أهداف',
                'priority': 'عالي',
                'message': f"🎯 تم تحقيق الهدف الشهري! {progress:.0f}%",
                'details': f"المبيعات: {format_currency(monthly_sales)}",
                'color': "#28a745",
                'timestamp': datetime.now(),
                'action': 'view_monthly_report',
                'action_data': {'month': month_start}
            })
        elif progress < expected_progress - 20:  # متأخر عن المتوقع بأكثر من 20%
            notifications.append({
                'type': 'أهداف',
                'priority': 'متوسط',
                'message': f"📊 تأخر في تحقيق الهدف الشهري: {progress:.0f}%",
                'details': f"المتوقع: {expected_progress:.0f}%، الفعلي: {progress:.0f}%",
                'color': "#ffc107",
                'timestamp': datetime.now(),
                'action': 'view_monthly_report',
                'action_data': {'month': month_start}
            })
        
        return notifications


class NotificationWidget(QFrame):
    """ودجت عرض التنبيه الواحد"""
    
    action_clicked = pyqtSignal(str, dict)
    dismiss_clicked = pyqtSignal(object)
    
    def __init__(self, notification_data):
        super().__init__()
        self.notification_data = notification_data
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة التنبيه"""
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {self.notification_data['color']};
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
                border: 1px solid rgba(255, 255, 255, 0.3);
            }}
        """)
        
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # الصف الأول: النوع والأولوية والوقت
        header_layout = QHBoxLayout()
        
        type_label = QLabel(f"📋 {self.notification_data['type']}")
        type_label.setStyleSheet("color: white; font-weight: bold; font-size: 12px;")
        
        priority_label = QLabel(f"🔥 {self.notification_data['priority']}")
        priority_label.setStyleSheet("color: white; font-weight: bold; font-size: 12px;")
        
        time_label = QLabel(self.notification_data['timestamp'].strftime("%H:%M"))
        time_label.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-size: 11px;")
        
        header_layout.addWidget(type_label)
        header_layout.addWidget(priority_label)
        header_layout.addStretch()
        header_layout.addWidget(time_label)
        
        layout.addLayout(header_layout)
        
        # الرسالة الرئيسية
        message_label = QLabel(self.notification_data['message'])
        message_label.setStyleSheet("color: white; font-weight: bold; font-size: 14px;")
        message_label.setWordWrap(True)
        layout.addWidget(message_label)
        
        # التفاصيل
        if self.notification_data.get('details'):
            details_label = QLabel(self.notification_data['details'])
            details_label.setStyleSheet("color: rgba(255, 255, 255, 0.9); font-size: 12px;")
            details_label.setWordWrap(True)
            layout.addWidget(details_label)
        
        # أزرار العمل
        buttons_layout = QHBoxLayout()
        
        if self.notification_data.get('action'):
            action_btn = QPushButton("عرض التفاصيل")
            action_btn.setStyleSheet("""
                QPushButton {
                    background-color: rgba(255, 255, 255, 0.2);
                    color: white;
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    padding: 5px 10px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: rgba(255, 255, 255, 0.3);
                }
            """)
            action_btn.clicked.connect(self.on_action_clicked)
            buttons_layout.addWidget(action_btn)
        
        dismiss_btn = QPushButton("إخفاء")
        dismiss_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 0.1);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                padding: 5px 10px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }
        """)
        dismiss_btn.clicked.connect(self.on_dismiss_clicked)
        buttons_layout.addWidget(dismiss_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
    
    def on_action_clicked(self):
        """عند النقر على زر العمل"""
        self.action_clicked.emit(
            self.notification_data['action'],
            self.notification_data.get('action_data', {})
        )
    
    def on_dismiss_clicked(self):
        """عند النقر على زر الإخفاء"""
        self.dismiss_clicked.emit(self)


class SmartNotificationSystem(QWidget):
    """نظام التنبيهات الذكي"""
    
    def __init__(self, engine, parent=None):
        super().__init__(parent)
        self.engine = engine
        self.parent_window = parent
        self.notifications = []
        self.checker_thread = None
        self.setup_ui()
        self.setup_system_tray()
        self.start_notification_checker()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # العنوان
        title_label = QLabel("🔔 التنبيهات الذكية")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # منطقة التنبيهات
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; }")
        
        self.notifications_widget = QWidget()
        self.notifications_layout = QVBoxLayout()
        self.notifications_widget.setLayout(self.notifications_layout)
        
        scroll_area.setWidget(self.notifications_widget)
        layout.addWidget(scroll_area)
        
        # رسالة عدم وجود تنبيهات
        self.no_notifications_label = QLabel("✅ لا توجد تنبيهات حالياً")
        self.no_notifications_label.setAlignment(Qt.AlignCenter)
        self.no_notifications_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 16px;
                padding: 50px;
            }
        """)
        self.notifications_layout.addWidget(self.no_notifications_label)
    
    def setup_system_tray(self):
        """إعداد أيقونة النظام"""
        if QSystemTrayIcon.isSystemTrayAvailable():
            self.tray_icon = QSystemTrayIcon(self)
            
            # إنشاء أيقونة بسيطة
            pixmap = QPixmap(16, 16)
            pixmap.fill(QColor("#007bff"))
            self.tray_icon.setIcon(QIcon(pixmap))
            
            # قائمة النقر الأيمن
            tray_menu = QMenu()
            
            show_action = QAction("عرض التنبيهات", self)
            show_action.triggered.connect(self.show_notifications)
            tray_menu.addAction(show_action)
            
            quit_action = QAction("إغلاق", self)
            quit_action.triggered.connect(self.parent_window.close if self.parent_window else self.close)
            tray_menu.addAction(quit_action)
            
            self.tray_icon.setContextMenu(tray_menu)
            self.tray_icon.show()
    
    def start_notification_checker(self):
        """بدء فاحص التنبيهات"""
        self.checker_thread = NotificationChecker(self.engine)
        self.checker_thread.notification_found.connect(self.add_notification)
        self.checker_thread.start()
    
    def add_notification(self, notification_data):
        """إضافة تنبيه جديد"""
        # تجنب التنبيهات المكررة
        for existing in self.notifications:
            if (existing['type'] == notification_data['type'] and 
                existing['message'] == notification_data['message']):
                return
        
        self.notifications.append(notification_data)
        
        # إخفاء رسالة عدم وجود تنبيهات
        self.no_notifications_label.hide()
        
        # إنشاء ودجت التنبيه
        notification_widget = NotificationWidget(notification_data)
        notification_widget.action_clicked.connect(self.handle_notification_action)
        notification_widget.dismiss_clicked.connect(self.dismiss_notification)
        
        self.notifications_layout.addWidget(notification_widget)
        
        # إشعار النظام
        if hasattr(self, 'tray_icon') and self.tray_icon.isVisible():
            self.tray_icon.showMessage(
                f"تنبيه {notification_data['type']}",
                notification_data['message'],
                QSystemTrayIcon.Information,
                5000
            )
    
    def dismiss_notification(self, notification_widget):
        """إخفاء تنبيه"""
        self.notifications_layout.removeWidget(notification_widget)
        notification_widget.deleteLater()
        
        # إظهار رسالة عدم وجود تنبيهات إذا لم تعد هناك تنبيهات
        if self.notifications_layout.count() == 0:
            self.no_notifications_label.show()
    
    def handle_notification_action(self, action, action_data):
        """معالجة إجراء التنبيه"""
        if action == 'view_product' and self.parent_window:
            # فتح صفحة المنتج
            self.parent_window.show_products()
        elif action == 'view_sales_report' and self.parent_window:
            # فتح تقرير المبيعات
            self.parent_window.show_sales_report()
        elif action == 'view_customer' and self.parent_window:
            # فتح صفحة العملاء
            self.parent_window.show_customers()
        elif action == 'view_monthly_report' and self.parent_window:
            # فتح التقرير الشهري
            self.parent_window.show_financial_reports()
    
    def show_notifications(self):
        """عرض نافذة التنبيهات"""
        self.show()
        self.raise_()
        self.activateWindow()
    
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        if hasattr(self, 'checker_thread') and self.checker_thread:
            self.checker_thread.stop()
            self.checker_thread.quit()
            self.checker_thread.wait()
        
        event.accept()
