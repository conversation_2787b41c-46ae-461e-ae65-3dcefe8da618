#!/usr/bin/env python3
"""
نظام طباعة الفواتير - التصميم الجديد
يطابق التصميم المطلوب في الصورة
"""

import os
import tempfile
import json
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                             QLabel, QRadioButton, QButtonGroup, QMessageBox,
                             QSplitter, QFrame, QTextBrowser)
from PyQt5.QtCore import Qt, QUrl, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QBrush, QPen, QColor, QPixmap, QPainter
from PyQt5.QtCore import QRect
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
from sqlalchemy.orm import sessionmaker
from database.models import Transaction, Customer, TransactionItem, Product, Supplier, TransactionType
from utils.company_settings import get_company_settings
from main import resource_path
import sys


class NewDesignInvoicePrinter(QDialog):
    """نافذة طباعة الفواتير بالتصميم الجديد"""
    
    def __init__(self, engine, invoice_id, parent=None):
        super().__init__(parent)
        self.engine = engine
        self.invoice_id = invoice_id
        self.invoice_data = None
        self.printer_type = "a4"  # افتراضي A4
        
        self.setWindowTitle("طباعة الفاتورة - التصميم الجديد")
        self.setGeometry(100, 100, 1200, 800)
        self.setModal(True)
        
        self.setup_ui()
        self.load_invoice_data()
        self.update_preview()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # عنوان النافذة
        title_label = QLabel("طباعة الفاتورة - التصميم الجديد")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2C3E50;
                padding: 15px;
                background-color: #ECF0F1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # اختيار نوع الطابعة
        printer_frame = QFrame()
        printer_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 2px solid #DEE2E6;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        printer_layout = QHBoxLayout()
        printer_frame.setLayout(printer_layout)
        
        printer_label = QLabel("نوع الطابعة:")
        printer_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #495057;")
        printer_layout.addWidget(printer_label)
        
        self.printer_group = QButtonGroup()
        
        self.a4_radio = QRadioButton("طابعة A4")
        self.a4_radio.setChecked(True)
        self.a4_radio.setStyleSheet("""
            QRadioButton {
                font-size: 14px;
                font-weight: bold;
                color: #495057;
                padding: 5px;
            }
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        self.a4_radio.toggled.connect(self.on_printer_type_changed)
        
        self.roll_radio = QRadioButton("طابعة رول (80مم)")
        self.roll_radio.setStyleSheet("""
            QRadioButton {
                font-size: 14px;
                font-weight: bold;
                color: #495057;
                padding: 5px;
            }
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        self.roll_radio.toggled.connect(self.on_printer_type_changed)
        
        self.printer_group.addButton(self.a4_radio)
        self.printer_group.addButton(self.roll_radio)
        
        printer_layout.addWidget(self.a4_radio)
        printer_layout.addWidget(self.roll_radio)
        printer_layout.addStretch()
        
        layout.addWidget(printer_frame)
        
        # منطقة المعاينة
        self.web_view = QTextBrowser()
        self.web_view.setStyleSheet("""
            QTextBrowser {
                border: 2px solid #DEE2E6;
                border-radius: 8px;
                background-color: white;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.web_view)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        # زر المعاينة
        preview_btn = QPushButton("🔍 معاينة")
        preview_btn.setStyleSheet(self.get_button_style("#17A2B8"))
        preview_btn.clicked.connect(self.update_preview)
        buttons_layout.addWidget(preview_btn)
        
        # زر حفظ PDF
        save_pdf_btn = QPushButton("💾 حفظ PDF")
        save_pdf_btn.setStyleSheet(self.get_button_style("#28A745"))
        save_pdf_btn.clicked.connect(self.save_as_pdf)
        buttons_layout.addWidget(save_pdf_btn)
        
        # زر الطباعة
        print_btn = QPushButton("🖨️ طباعة")
        print_btn.setStyleSheet(self.get_button_style("#007BFF"))
        print_btn.clicked.connect(self.print_invoice)
        buttons_layout.addWidget(print_btn)
        
        # زر الإغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet(self.get_button_style("#DC3545"))
        close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(close_btn)
        
        layout.addLayout(buttons_layout)
    
    def get_button_style(self, color):
        """إرجاع نمط الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                font-size: 24px;
                font-weight: bold;
                padding: 12px 20px;
                border: none;
                border-radius: 6px;
                min-width: 120px;
                min-height: 40px;
            }}
            QPushButton:hover {{
                background-color: {color}DD;
                transform: translateY(-1px);
            }}
            QPushButton:pressed {{
                background-color: {color}BB;
            }}
        """
    
    def on_printer_type_changed(self):
        """تغيير نوع الطابعة"""
        if self.a4_radio.isChecked():
            self.printer_type = "a4"
        else:
            self.printer_type = "roll"
        self.update_preview()
    
    def load_invoice_data(self):
        """تحميل بيانات الفاتورة"""
        try:
            Session = sessionmaker(bind=self.engine)
            session = Session()
            
            # جلب الفاتورة
            invoice = session.query(Transaction).filter_by(id=self.invoice_id).first()
            if not invoice:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على الفاتورة")
                return
            
            # جلب العميل أو المورد حسب نوع الفاتورة
            customer = None
            supplier = None
            if invoice.type == TransactionType.SALE and invoice.customer_id:
                customer = session.query(Customer).filter_by(id=invoice.customer_id).first()
            elif invoice.type == TransactionType.PURCHASE and invoice.supplier_id:
                supplier = session.query(Supplier).filter_by(id=invoice.supplier_id).first()

            # جلب عناصر الفاتورة
            items = session.query(TransactionItem).filter_by(transaction_id=self.invoice_id).all()

            # تحضير البيانات
            self.invoice_data = {
                'id': invoice.id,
                'type': invoice.type,
                'date': invoice.date.strftime('%Y-%m-%d') if invoice.date else '',
                'total_amount': float(invoice.total_amount),
                'paid_amount': float(invoice.paid_amount),
                'discount': float(invoice.discount) if invoice.discount else 0,
                'remaining_amount': float(invoice.total_amount - invoice.paid_amount),
                'customer_name': customer.name if customer else (supplier.name if supplier else 'عميل نقدي'),
                'customer_phone': customer.phone if customer else (supplier.phone if supplier else ''),
                'customer_address': customer.address if customer else (supplier.address if supplier else ''),
                'items': []
            }
            
            # إضافة المنتجات
            for item in items:
                product = session.query(Product).filter_by(id=item.product_id).first()
                if product:
                    self.invoice_data['items'].append({
                        'name': product.name,
                        'quantity': int(item.quantity),
                        'price': float(item.price),
                        'total': float(item.quantity * item.price),
                        'unit': product.unit if product.unit else 'قطعة'
                    })
            
            session.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الفاتورة:\n{str(e)}")
    
    def update_preview(self):
        """تحديث المعاينة"""
        try:
            if not self.invoice_data:
                return

            html_content = self.generate_invoice_html()
            
            if not html_content:
                self.web_view.setHtml("<p>لا توجد بيانات للعرض</p>")
                return

            # عرض HTML في المتصفح النصي
            self.web_view.setHtml(html_content)
            
        except Exception as e:
            print(f"خطأ في تحديث المعاينة: {e}")
            error_html = f"""
            <html>
            <body style="font-family: Arial; text-align: center; padding: 20px;">
                <h3>خطأ في تحديث المعاينة</h3>
                <p>{str(e)}</p>
                <p>يرجى المحاولة مرة أخرى</p>
            </body>
            </html>
            """
            self.web_view.setHtml(error_html)
    
    def generate_invoice_html(self):
        """إنشاء HTML للفاتورة حسب نوع الطابعة المختار"""
        if not self.invoice_data:
            return ""

        if self.printer_type == "a4":
            return self.generate_print_optimized_html()
        else:
            return self.generate_roll_invoice_html()

    def generate_print_optimized_html(self):
        """إنشاء HTML محسن للطباعة مع الحفاظ على التصميم الجميل"""
        if not self.invoice_data:
            return ""

        # تحميل إعدادات الشركة
        company_settings = get_company_settings()

        # معلومات الشركة
        company_name = company_settings.get("company_name", "هوم سنتر للأدوات المنزلية")
        company_address = company_settings.get("address", "حي الصفا - جدة - المملكة العربية السعودية")
        company_phone = company_settings.get("phone", "01010101010")
        company_email = company_settings.get("email", "<EMAIL>")

        # الشعار
        logo_html = ""
        if company_settings.get("logo_base64"):
            logo_html = f'<img src="data:image/png;base64,{company_settings["logo_base64"]}" class="logo">'
        elif company_settings.get("logo_path") and os.path.exists(company_settings["logo_path"]):
            import base64
            with open(company_settings["logo_path"], "rb") as img_file:
                encoded = base64.b64encode(img_file.read()).decode('utf-8')
                logo_html = f'<img src="data:image/png;base64,{encoded}" class="logo">'
        else:
            # استخدام لوجو افتراضي
            import base64
            from main import resource_path
            default_logo_path = resource_path(os.path.join('assets', 'company01_logo.png'))
            if os.path.exists(default_logo_path):
                with open(default_logo_path, "rb") as img_file:
                    encoded = base64.b64encode(img_file.read()).decode('utf-8')
                    logo_html = f'<img src="data:image/png;base64,{encoded}" class="logo">'
            else:
                logo_html = '''
            <div class="default-logo">
                <div class="logo-icon">🏢</div>
            </div>'''

        # تحضير بيانات المنتجات
        products_html = ""
        for item in self.invoice_data['items']:
            products_html += f"""
                <tr>
                    <td class="product-name">{item['name']}</td>
                    <td>{item['quantity']}</td>
                    <td>{item['price']:,.0f}</td>
                    <td>{item['unit']}</td>
                    <td>{item['total']:,.0f}</td>
                </tr>
            """

        html = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة رقم {self.invoice_data['id']:06d}</title>
            <style>
                @page {{
                    size: A4;
                    margin: 15mm;
                }}

                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}

                body {{
                    font-family: 'Arial', 'Tahoma', sans-serif;
                    font-size: 14px;
                    line-height: 1.6;
                    color: #000;
                    direction: rtl;
                    background: white;
                }}

                .invoice-container {{
                    width: 100%;
                    max-width: 210mm;
                    margin: 0 auto;
                    background: white;
                    padding: 20px;
                }}

                /* الترويسة */
                .header {{
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 30px;
                    padding-bottom: 20px;
                    border-bottom: 3px solid #27AE60;
                }}

                .logo-section {{
                    flex: 1;
                    text-align: left;
                }}

                .default-logo {{
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }}

                .logo-icon {{
                    font-size: 40px;
                    color: #27AE60;
                }}

                .logo-text {{
                    display: flex;
                    flex-direction: column;
                }}

                .logo-main {{
                    font-size: 28px;
                    font-weight: bold;
                    color: #2C3E50;
                    line-height: 1;
                }}

                .logo-sub {{
                    font-size: 14px;
                    color: #7F8C8D;
                    letter-spacing: 2px;
                    margin-top: -2px;
                }}

                .company-info {{
                    flex: 2;
                    text-align: right;
                    padding-right: 20px;
                }}

                .company-name {{
                    font-size: 24px;
                    font-weight: bold;
                    color: #2C3E50;
                    margin-bottom: 10px;
                }}

                .company-details {{
                    font-size: 14px;
                    color: #34495E;
                    line-height: 1.8;
                }}

                .company-details div {{
                    margin-bottom: 5px;
                }}

                /* عنوان الفاتورة */
                .invoice-title {{
                    text-align: center;
                    background: linear-gradient(135deg, #3498DB, #2980B9);
                    color: white;
                    padding: 15px;
                    margin: 20px 0;
                    border-radius: 8px;
                    font-size: 20px;
                    font-weight: bold;
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                }}

                /* جدول المنتجات */
                .products-section {{
                    margin: 30px 0;
                }}

                .section-title {{
                    background: linear-gradient(135deg, #27AE60, #2ECC71);
                    color: white;
                    padding: 12px 20px;
                    margin-bottom: 0;
                    font-size: 18px;
                    font-weight: bold;
                    border-radius: 8px 8px 0 0;
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                }}

                .products-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                    border: 2px solid #27AE60;
                    border-radius: 0 0 8px 8px;
                    overflow: hidden;
                }}

                .products-table th {{
                    background: linear-gradient(135deg, #27AE60, #2ECC71);
                    color: white;
                    padding: 15px 10px;
                    text-align: center;
                    font-weight: bold;
                    font-size: 16px;
                    border-bottom: 2px solid #229954;
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                }}

                .products-table td {{
                    padding: 12px 10px;
                    border-bottom: 1px solid #BDC3C7;
                    font-size: 14px;
                }}

                .products-table tr:nth-child(even) {{
                    background-color: #F8F9FA;
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                }}

                .product-name {{
                    text-align: right;
                    font-weight: bold;
                    color: #2C3E50;
                }}

                .center {{
                    text-align: center;
                }}

                /* تفاصيل الفاتورة */
                .invoice-details {{
                    background: linear-gradient(135deg, #F8FAFE, #EBF3FD);
                    border: 2px solid #3498DB;
                    border-radius: 12px;
                    padding: 25px;
                    margin-top: 30px;
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                }}

                .details-title {{
                    background: linear-gradient(135deg, #3498DB, #2980B9);
                    color: white;
                    padding: 12px 20px;
                    margin: -25px -25px 20px -25px;
                    border-radius: 10px 10px 0 0;
                    font-size: 18px;
                    font-weight: bold;
                    text-align: center;
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                }}

                .details-grid {{
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 20px;
                    font-size: 16px;
                }}

                .detail-item {{
                    display: flex;
                    justify-content: space-between;
                    padding: 8px 0;
                    border-bottom: 1px solid #D5DBDB;
                }}

                .detail-label {{
                    font-weight: bold;
                    color: #2C3E50;
                }}

                .detail-value {{
                    color: #34495E;
                }}

                .total-amount {{
                    grid-column: 1 / -1;
                    background: linear-gradient(135deg, #E8F6F3, #D5F4E6);
                    padding: 15px;
                    border-radius: 8px;
                    border: 2px solid #27AE60;
                    margin-top: 15px;
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                }}

                .total-amount .detail-item {{
                    font-size: 18px;
                    font-weight: bold;
                    color: #27AE60;
                    border: none;
                }}

                /* تذييل */
                .footer {{
                    text-align: center;
                    margin-top: 40px;
                    padding-top: 20px;
                    border-top: 2px solid #BDC3C7;
                    color: #7F8C8D;
                    font-size: 14px;
                }}

                /* تحسينات الطباعة */
                @media print {{
                    body {{
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                    }}

                    .invoice-container {{
                        box-shadow: none;
                        border: none;
                    }}
                }}
            </style>
        </head>
        <body>
            <div class="invoice-container">
                <!-- الترويسة -->
                <div class="header">
                    <div class="logo-section">
                        {logo_html}
                    </div>

                    <div class="company-info">
                        <div class="company-name">{company_name}</div>
                        <div class="company-details">
                            <div>📍 {company_address}</div>
                            <div>📞 {company_phone}</div>
                            <div>📧 {company_email}</div>
                        </div>
                    </div>
                </div>

                <!-- عنوان الفاتورة -->
                <div class="invoice-title">
                    {'🛒 فاتورة مشتريات' if self.invoice_data['type'] == TransactionType.PURCHASE else '🧾 فاتورة مبيعات'} رقم {self.invoice_data['id']:06d}
                </div>

                <!-- جدول المنتجات -->
                <div class="products-section">
                    <div class="section-title">🛍️ تفاصيل المنتجات</div>
                    <table class="products-table">
                        <thead>
                            <tr>
                                <th style="width: 15%">💵 الإجمالي</th>
                                <th style="width: 18%">📦 الوحدة</th>
                                <th style="width: 15%">💰 السعر</th>
                                <th style="width: 12%">📊 الكمية</th>
                                <th style="width: 40%">🛍️ المنتج</th>
                            </tr>
                        </thead>
                        <tbody>
                            {products_html}
                        </tbody>
                    </table>
                </div>

                <!-- تفاصيل الفاتورة -->
                <div class="invoice-details">
                    <div class="details-title">📋 تفاصيل الفاتورة</div>
                    <div class="details-grid">
                        <div class="detail-item">
                            <span class="detail-label">📅 التاريخ:</span>
                            <span class="detail-value">{self.invoice_data['date']}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">{'🏭 المورد:' if self.invoice_data['type'] == TransactionType.PURCHASE else '👤 العميل:'}</span>
                            <span class="detail-value">{self.invoice_data['customer_name']}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">💰 المجموع الفرعي:</span>
                            <span class="detail-value">{sum(item['total'] for item in self.invoice_data['items']):,.0f} ج.م</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">💳 المدفوع:</span>
                            <span class="detail-value">{self.invoice_data['paid_amount']:,.0f} ج.م</span>
                        </div>
                        {f'<div class="detail-item"><span class="detail-label">🎯 الخصم:</span><span class="detail-value">{self.invoice_data["discount"]:,.0f} ج.م</span></div>' if self.invoice_data['discount'] > 0 else ''}
                        {f'<div class="detail-item"><span class="detail-label">⏳ المتبقي:</span><span class="detail-value">{self.invoice_data["remaining_amount"]:,.0f} ج.م</span></div>' if self.invoice_data['remaining_amount'] > 0 else ''}

                        <div class="total-amount">
                            <div class="detail-item">
                                <span class="detail-label">💎 الإجمالي النهائي:</span>
                                <span class="detail-value">{self.invoice_data['total_amount']:,.0f} ج.م</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- التذييل -->
                <div class="footer">
                    <p>شكراً لتعاملكم معنا</p>
                    <p>نتطلع لخدمتكم مرة أخرى</p>
                </div>
            </div>
        </body>
        </html>
        """

        return html

    def generate_new_design_html(self):
        """إنشاء HTML بالتصميم الجديد المطابق للصورة"""
        if not self.invoice_data:
            return ""

        # تحميل إعدادات الشركة
        company_settings = get_company_settings()

        # معلومات الشركة
        company_name = company_settings.get("company_name", "هوم سنتر للأدوات المنزلية")
        company_address = company_settings.get("address", "")
        company_phone = company_settings.get("phone", "01010101010")
        company_email = company_settings.get("email", "")

        # الشعار
        logo_html = ""
        if company_settings.get("logo_base64"):
            logo_html = f'<img src="data:image/png;base64,{company_settings["logo_base64"]}" class="logo">'
        elif company_settings.get("logo_path") and os.path.exists(company_settings["logo_path"]):
            import base64
            with open(company_settings["logo_path"], "rb") as img_file:
                encoded = base64.b64encode(img_file.read()).decode('utf-8')
                logo_html = f'<img src="data:image/png;base64,{encoded}" class="logo">'
        else:
            # استخدام لوجو افتراضي
            logo_html = '''
            <div class="default-logo">
                <div class="logo-icon">🏠</div>
                <div class="logo-text">
                    <div class="logo-main">home</div>
                    <div class="logo-sub">CENTER</div>
                </div>
            </div>
            '''

        # تحضير بيانات المنتجات
        products_html = ""
        for item in self.invoice_data['items']:
            products_html += f"""
                <tr>
                    <td>{item['total']:,.0f}</td>
                    <td>{item['unit']}</td>
                    <td>{item['price']:,.0f}</td>
                    <td>{item['quantity']}</td>
                    <td class="product-name">{item['name']}</td>
                </tr>
            """

        html = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة رقم {self.invoice_data['id']:06d}</title>
            <style>
                @page {{
                    size: A4;
                    margin: 10mm;
                }}

                * {{
                    box-sizing: border-box;
                    margin: 0;
                    padding: 0;
                }}

                body {{
                    font-family: 'Arial', 'Tahoma', sans-serif;
                    font-size: 26px;
                    font-weight: bold;
                    line-height: 1.4;
                    color: #000;
                    direction: rtl;
                    background-color: white;
                }}

                .invoice-container {{
                    width: 100%;
                    max-width: 210mm;
                    margin: 0 auto;
                    background-color: white;
                    padding: 15px;
                }}

                /* الترويسة */
                .header {{
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 30px;
                    padding-bottom: 20px;
                }}

                .logo-section {{
                    flex: 1;
                    text-align: left;
                }}

                .default-logo {{
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }}

                .logo-icon {{
                    font-size: 48px;
                    color: #2C3E50;
                }}

                .logo-text {{
                    display: flex;
                    flex-direction: column;
                }}

                .logo-main {{
                    font-size: 32px;
                    font-weight: bold;
                    color: #2C3E50;
                    line-height: 1;
                }}

                .logo-sub {{
                    font-size: 26px;
                    font-weight: bold;
                    color: #7F8C8D;
                    letter-spacing: 2px;
                    margin-top: -5px;
                }}

                .logo {{
                    max-width: 120px;
                    max-height: 80px;
                    object-fit: contain;
                }}

                .company-info {{
                    flex: 1;
                    text-align: right;
                    padding-left: 30px;
                }}

                .company-name {{
                    font-size: 24px;
                    font-weight: bold;
                    color: #2C3E50;
                    margin-bottom: 10px;
                }}

                .company-details {{
                    font-size: 24px;
                    font-weight: bold;
                    color: #34495E;
                    line-height: 1.6;
                }}

                .company-details div {{
                    margin-bottom: 5px;
                }}

                /* جدول المنتجات الأخضر */
                .products-section {{
                    margin: 30px 0;
                }}

                .products-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                }}

                .products-table th {{
                    background: linear-gradient(135deg, #27AE60, #2ECC71);
                    color: white;
                    padding: 15px 10px;
                    text-align: center;
                    font-weight: bold;
                    font-size: 16px;
                    border: 1px solid #27AE60;
                }}

                .products-table td {{
                    padding: 12px 10px;
                    text-align: center;
                    border: 1px solid #BDC3C7;
                    font-size: 14px;
                    background-color: white;
                }}

                .product-name {{
                    text-align: right !important;
                    font-weight: bold;
                    padding-right: 15px !important;
                }}

                .products-table tr:nth-child(even) {{
                    background-color: #F8F9FA;
                }}

                .products-table tr:hover {{
                    background-color: #E8F5E8;
                }}

                /* تفاصيل الفاتورة السفلية */
                .invoice-details {{
                    margin-top: 30px;
                    border: 2px solid #3498DB;
                    border-radius: 8px;
                    padding: 20px;
                    background-color: #F8FAFE;
                }}

                .details-grid {{
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 20px;
                    align-items: start;
                }}

                .detail-item {{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 8px 12px;
                    background-color: white;
                    border: 1px solid #BDC3C7;
                    border-radius: 5px;
                    margin-bottom: 8px;
                }}

                .detail-label {{
                    font-weight: bold;
                    color: #2C3E50;
                    font-size: 14px;
                }}

                .detail-value {{
                    color: #3498DB;
                    font-weight: bold;
                    font-size: 14px;
                }}

                .invoice-number {{
                    grid-column: 1 / -1;
                    text-align: center;
                    background: linear-gradient(135deg, #3498DB, #5DADE2);
                    color: white;
                    padding: 12px;
                    border-radius: 5px;
                    font-size: 18px;
                    font-weight: bold;
                    margin-bottom: 15px;
                }}

                @media print {{
                    body {{
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                    }}

                    .invoice-container {{
                        padding: 0;
                    }}
                }}
            </style>
        </head>
        <body>
            <div class="invoice-container">
                <!-- الترويسة -->
                <div class="header">
                    <div class="logo-section">
                        {logo_html}
                    </div>
                    <div class="company-info">
                        <div class="company-name">{company_name}</div>
                        <div class="company-details">
                            <div>📍 {company_address if company_address else 'مكة المكرمة - حي النسيم'}</div>
                            <div>📞 {company_phone}</div>
                            <div>📧 {company_email if company_email else '*********'}</div>
                        </div>
                    </div>
                </div>

                <!-- جدول المنتجات الأخضر -->
                <div class="products-section">
                    <table class="products-table">
                        <thead>
                            <tr>
                                <th style="width: 15%">💵 الإجمالي</th>
                                <th style="width: 18%">📦 الوحدة</th>
                                <th style="width: 15%">💰 السعر</th>
                                <th style="width: 12%">📊 الكمية</th>
                                <th style="width: 40%">🛍️ المنتج</th>
                            </tr>
                        </thead>
                        <tbody>
                            {products_html}
                        </tbody>
                    </table>
                </div>

                <!-- تفاصيل الفاتورة السفلية -->
                <div class="invoice-details">
                    <div class="invoice-number">
                        📋 تفاصيل الفاتورة رقم {self.invoice_data['id']:06d}
                    </div>

                    <div class="details-grid">
                        <div class="detail-item">
                            <span class="detail-label">📅 التاريخ:</span>
                            <span class="detail-value">{self.invoice_data['date']}</span>
                        </div>

                        <div class="detail-item">
                            <span class="detail-label">{'🏭 المورد:' if self.invoice_data['type'] == TransactionType.PURCHASE else '👤 العميل:'}</span>
                            <span class="detail-value">{self.invoice_data['customer_name']}</span>
                        </div>

                        <div class="detail-item">
                            <span class="detail-label">💰 المجموع الفرعي:</span>
                            <span class="detail-value">{sum(item['total'] for item in self.invoice_data['items']):,.0f} ج.م</span>
                        </div>

                        <div class="detail-item">
                            <span class="detail-label">🎯 الخصم:</span>
                            <span class="detail-value">{self.invoice_data['discount']:,.0f} ج.م</span>
                        </div>

                        <div class="detail-item">
                            <span class="detail-label">💵 الإجمالي النهائي:</span>
                            <span class="detail-value">{self.invoice_data['total_amount']:,.0f} ج.م</span>
                        </div>

                        <div class="detail-item">
                            <span class="detail-label">💳 المدفوع:</span>
                            <span class="detail-value">{self.invoice_data['paid_amount']:,.0f} ج.م</span>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        """

        return html

    def generate_roll_invoice_html(self):
        """إنشاء HTML للطباعة على ورق الرول مع معالجة استثناءات قوية"""
        try:
            if not self.invoice_data:
                return "<p>لا توجد بيانات للفاتورة</p>"

            # تحميل إعدادات الشركة
            company_settings = get_company_settings()

            # معلومات الشركة
            company_name = company_settings.get("company_name", "هوم سنتر للأدوات المنزلية")
            company_phone = company_settings.get("phone", "01010101010")

            # تحضير بيانات المنتجات
            products_html = ""
            for item in self.invoice_data.get('items', []):
                name = item.get('name', '-')
                quantity = item.get('quantity', 0) or 0
                price = item.get('price', 0) or 0
                unit = item.get('unit', '-')
                total = item.get('total', 0) or 0
                products_html += f"""
                    <tr>
                        <td>{total:,.0f}</td>
                        <td>{unit}</td>
                        <td>{price:,.0f}</td>
                        <td>{quantity}</td>
                        <td class=\"product-name\">{name}</td>
                    </tr>
                """

            invoice_id = self.invoice_data.get('id', 0)
            invoice_type = self.invoice_data.get('type', None)
            customer_name = self.invoice_data.get('customer_name', '-')
            date = self.invoice_data.get('date', '-')
            discount = self.invoice_data.get('discount', 0) or 0
            total_amount = self.invoice_data.get('total_amount', 0) or 0
            paid_amount = self.invoice_data.get('paid_amount', 0) or 0
            remaining_amount = self.invoice_data.get('remaining_amount', 0) or 0

            html = f"""
            <!DOCTYPE html>
            <html dir=\"rtl\" lang=\"ar\">
            <head>
                <meta charset=\"UTF-8\">
                <title>فاتورة رقم {invoice_id:06d}</title>
                <style>
                    @page {{
                        size: 80mm auto;
                        margin: 5mm;
                    }}
                    body {{
                        font-family: 'Arial', 'Tahoma', sans-serif;
                        font-size: 24px;
                        font-weight: bold;
                        line-height: 1.4;
                        color: #000;
                        margin: 0;
                        padding: 0;
                        width: 70mm;
                        direction: rtl;
                    }}
                    .header {{
                        text-align: center;
                        border-bottom: 2px solid #000;
                        padding-bottom: 10px;
                        margin-bottom: 15px;
                    }}
                    .company-name {{
                        font-size: 18px;
                        font-weight: bold;
                        margin-bottom: 5px;
                    }}
                    .company-phone {{
                        font-size: 24px;
                        font-weight: bold;
                        color: #666;
                    }}
                    .invoice-title {{
                        text-align: center;
                        font-size: 26px;
                        font-weight: bold;
                        margin: 15px 0;
                        border: 2px solid #000;
                        padding: 8px;
                        background-color: #f0f0f0;
                    }}
                    .customer-info {{
                        margin-bottom: 15px;
                        font-size: 12px;
                        border-bottom: 2px dashed #000;
                        padding-bottom: 10px;
                    }}
                    .products-table {{
                        width: 100%;
                        border-collapse: collapse;
                        font-size: 11px;
                        margin-bottom: 15px;
                    }}
                    .products-table th {{
                        background-color: #000;
                        color: white;
                        padding: 5px;
                        text-align: center;
                        font-size: 11px;
                    }}
                    .products-table td {{
                        padding: 4px;
                        text-align: center;
                        border: 1px solid #000;
                        font-size: 11px;
                    }}
                    .product-name {{
                        text-align: right !important;
                        font-weight: bold;
                    }}
                    .totals {{
                        border-top: 2px solid #000;
                        padding-top: 10px;
                        font-size: 12px;
                    }}
                    .total-row {{
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 5px;
                    }}
                    .final-total {{
                        font-weight: bold;
                        font-size: 14px;
                        border-top: 2px solid #000;
                        padding-top: 5px;
                        margin-top: 5px;
                    }}
                    .footer {{
                        text-align: center;
                        margin-top: 15px;
                        font-size: 10px;
                        border-top: 2px dashed #000;
                        padding-top: 8px;
                    }}
                </style>
            </head>
            <body>
                <div class=\"header\">
                    <div class=\"company-name\">{company_name}</div>
                    <div class=\"company-phone\">📞 {company_phone}</div>
                </div>
                <div class=\"invoice-title\">{'فاتورة مشتريات' if invoice_type == TransactionType.PURCHASE else 'فاتورة مبيعات'} رقم {invoice_id:06d}</div>
                <div class=\"customer-info\">
                    <strong>{'المورد:' if invoice_type == TransactionType.PURCHASE else 'العميل:'}</strong> {customer_name}<br>
                    <strong>التاريخ:</strong> {date}
                </div>
                <table class=\"products-table\">
                    <thead>
                        <tr>
                            <th style=\"width: 15%\">💵 الإجمالي</th>
                            <th style=\"width: 18%\">📦 الوحدة</th>
                            <th style=\"width: 15%\">💰 السعر</th>
                            <th style=\"width: 12%\">📊 الكمية</th>
                            <th style=\"width: 40%\">🛍️ المنتج</th>
                        </tr>
                    </thead>
                    <tbody>
                        {products_html}
                    </tbody>
                </table>
                <div class=\"totals\">
                    <div class=\"total-row\">
                        <span>المجموع الفرعي:</span>
                        <span>{sum(item.get('total', 0) or 0 for item in self.invoice_data.get('items', [])):,.0f} ج.م</span>
                    </div>
                    {f'<div class="total-row"><span>الخصم:</span><span>{discount:,.0f} ج.م</span></div>' if discount > 0 else ''}
                    <div class=\"total-row final-total\">
                        <span>الإجمالي النهائي:</span>
                        <span>{total_amount:,.0f} ج.م</span>
                    </div>
                    <div class=\"total-row\">
                        <span>المدفوع:</span>
                        <span>{paid_amount:,.0f} ج.م</span>
                    </div>
                    {f'<div class="total-row"><span>المتبقي:</span><span>{remaining_amount:,.0f} ج.م</span></div>' if remaining_amount > 0 else ''}
                </div>
                <div class=\"footer\">
                    شكراً لتعاملكم معنا
                </div>
            </body>
            </html>
            """
            return html
        except Exception as e:
            return f"<html><body style='color:red; font-size:18px; text-align:center;'><h3>خطأ في توليد معاينة الرول</h3><p>{str(e)}</p></body></html>"

    def save_as_pdf(self):
        """حفظ الفاتورة كملف PDF"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ الفاتورة كـ PDF",
                f"فاتورة_{self.invoice_data['id']:06d}.pdf",
                "PDF Files (*.pdf)"
            )

            if file_path:
                # إعداد الطابعة للـ PDF
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)

                if self.printer_type == "a4":
                    printer.setPageSize(QPrinter.A4)
                    printer.setPageMargins(10, 10, 10, 10, QPrinter.Millimeter)

                    # للـ A4: استخدام نفس حجم العرض
                    self.print_with_exact_preview_size(printer)
                else:
                    # للرول: يبقى زي ما هو
                    printer.setPageSize(QPrinter.Custom)
                    printer.setPageMargins(5, 5, 5, 5, QPrinter.Millimeter)

                    # طباعة عادية للرول
                    document = self.web_view.document()
                    document.print_(printer)
                QMessageBox.information(self, "نجح", f"تم حفظ الفاتورة في:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ PDF:\n{str(e)}")

    def print_invoice(self):
        """طباعة الفاتورة"""
        try:
            printer = QPrinter(QPrinter.HighResolution)

            if self.printer_type == "a4":
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(10, 10, 10, 10, QPrinter.Millimeter)
            else:
                printer.setPageSize(QPrinter.Custom)
                printer.setPageMargins(5, 5, 5, 5, QPrinter.Millimeter)

            # عرض نافذة اختيار الطابعة
            print_dialog = QPrintDialog(printer, self)
            if print_dialog.exec_() == QPrintDialog.Accepted:
                if self.printer_type == "a4":
                    # للـ A4: استخدام نفس حجم العرض
                    self.print_with_exact_preview_size(printer)
                else:
                    # للرول: طباعة عادية
                    document = self.web_view.document()
                    document.print_(printer)
                QMessageBox.information(self, "نجح", "تم إرسال الفاتورة للطباعة بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الطباعة:\n{str(e)}")

    def print_with_exact_preview_size(self, printer):
        """طباعة بنفس حجم وتنسيق العرض للـ A4"""
        try:
            # الحصول على حجم العرض الحالي
            view_size = self.web_view.size()

            # إنشاء صورة بنفس حجم العرض
            pixmap = QPixmap(view_size)
            pixmap.fill(QColor(255, 255, 255))  # خلفية بيضاء

            # رسم محتوى الـ web_view على الصورة
            self.web_view.render(pixmap)

            # بدء الطباعة
            painter = QPainter()
            painter.begin(printer)

            # حساب نسبة التكبير للحفاظ على النسب
            page_rect = printer.pageRect()
            scale_x = page_rect.width() / pixmap.width()
            scale_y = page_rect.height() / pixmap.height()
            scale = min(scale_x, scale_y)  # استخدام أصغر نسبة للحفاظ على النسب

            # حساب الحجم الجديد
            target_width = int(pixmap.width() * scale)
            target_height = int(pixmap.height() * scale)

            # توسيط الصورة في الصفحة
            x = (page_rect.width() - target_width) // 2
            y = (page_rect.height() - target_height) // 2

            # رسم الصورة مع التكبير المناسب
            target_rect = QRect(x, y, target_width, target_height)
            source_rect = QRect(0, 0, pixmap.width(), pixmap.height())

            painter.drawPixmap(target_rect, pixmap, source_rect)
            painter.end()

        except Exception as e:
            print(f"خطأ في الطباعة بحجم العرض: {e}")
            # في حالة الخطأ، استخدم الطريقة العادية
            document = self.web_view.document()
            document.print_(printer)

    def draw_a4_invoice(self, painter, width, height, company_name, company_address, company_phone, company_email):
        """رسم فاتورة A4 بتصميم جميل مع نصوص واضحة ولوجو مخصص"""
        y = 30

        # رسم الترويسة بالأبيض والأسود مع الحفاظ على التصميم
        header_height = 180  # زيادة الارتفاع من 120 إلى 180

        # خلفية بيضاء مع حدود سوداء
        painter.setBrush(QBrush(QColor(255, 255, 255)))  # خلفية بيضاء
        painter.setPen(QPen(QColor(0, 0, 0), 2))  # حدود سوداء
        painter.drawRoundedRect(30, y, width - 60, header_height, 12, 12)

        # رسم اللوجو المخصص أو الافتراضي مع حجم أكبر
        logo_x = 50
        logo_y = y + 20
        logo_size = 120  # زيادة حجم اللوجو من 90 إلى 120

        # محاولة تحميل اللوجو المحفوظ
        logo_loaded = False
        try:
            # قراءة إعدادات الشركة مباشرة من الملف
            if getattr(sys, 'frozen', False):
                # إذا كان البرنامج مصدر (PyInstaller)
                base_dir = os.path.dirname(sys.executable)
                settings_file = os.path.join(base_dir, "company_settings.json")
            else:
                # إذا كان البرنامج يعمل من الكود المصدري
                settings_file = "company_settings.json"

            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
                logo_path = settings.get('logo_path', '')
                logo_base64 = settings.get('logo_base64', '')

                if logo_base64:
                    # تحميل اللوجو من base64
                    image = QImage()
                    image.loadFromData(base64.b64decode(logo_base64))
                    logo_pixmap = QPixmap.fromImage(image)
                    if not logo_pixmap.isNull():
                        scaled_logo = logo_pixmap.scaled(logo_size, logo_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                        painter.drawPixmap(logo_x, logo_y, scaled_logo)
                        logo_loaded = True
                elif logo_path and os.path.exists(logo_path):
                    # تحميل اللوجو من المسار
                    logo_pixmap = QPixmap(logo_path)
                    if not logo_pixmap.isNull():
                        scaled_logo = logo_pixmap.scaled(logo_size, logo_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                        painter.drawPixmap(logo_x, logo_y, scaled_logo)
                        logo_loaded = True

        except Exception as e:
            print(f"خطأ في تحميل اللوجو: {e}")

        # إذا لم يتم تحميل اللوجو المخصص، استخدم الافتراضي
        if not logo_loaded:
            try:
                # محاولة تحميل اللوجو الافتراضي
                default_logo_path = resource_path(os.path.join('assets', 'company01_logo.png'))
                if os.path.exists(default_logo_path):
                    logo_pixmap = QPixmap(default_logo_path)
                    if not logo_pixmap.isNull():
                        scaled_logo = logo_pixmap.scaled(logo_size, logo_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                        painter.drawPixmap(logo_x, logo_y, scaled_logo)
                        logo_loaded = True
            except Exception as e:
                print(f"خطأ في تحميل اللوجو الافتراضي: {e}")

        # إذا فشل كل شيء، استخدم النص الافتراضي
        if not logo_loaded:
            painter.setPen(QPen(QColor(0, 0, 0), 2))
            painter.setFont(QFont("Arial", 32, QFont.Bold))
            painter.drawText(logo_x, logo_y + 50, "🏢")
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(logo_x, logo_y + 80, company_name)

    def draw_roll_invoice(self, painter, width, height, company_name, company_phone):
        """رسم فاتورة رول بتصميم محسن مع جميع التفاصيل"""
        y = 10

        # تحميل إعدادات الشركة الكاملة
        company_settings = get_company_settings()
        company_address = company_settings.get("address", "حي الصفا - جدة - المملكة العربية السعودية")
        company_email = company_settings.get("email", "<EMAIL>")

        # ترويسة محسنة بالأبيض والأسود مع ارتفاع أكبر (بدون حدود)
        header_height = 120  # ارتفاع أكبر للترويسة

        # رسم اللوجو المخصص أو الافتراضي (حجم أصغر للرول)
        logo_x = 10
        logo_y = y + 10
        logo_size = 60  # حجم مناسب للرول

        # محاولة تحميل اللوجو المحفوظ
        logo_loaded = False
        try:
            # قراءة إعدادات الشركة مباشرة من الملف
            if getattr(sys, 'frozen', False):
                # إذا كان البرنامج مصدر (PyInstaller)
                base_dir = os.path.dirname(sys.executable)
                settings_file = os.path.join(base_dir, "company_settings.json")
            else:
                # إذا كان البرنامج يعمل من الكود المصدري
                settings_file = "company_settings.json"

            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
                logo_path = settings.get('logo_path', '')
                logo_base64 = settings.get('logo_base64', '')

                if logo_base64:
                    # تحميل اللوجو من base64
                    image = QImage()
                    image.loadFromData(base64.b64decode(logo_base64))
                    logo_pixmap = QPixmap.fromImage(image)
                    if not logo_pixmap.isNull():
                        scaled_logo = logo_pixmap.scaled(logo_size, logo_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                        painter.drawPixmap(logo_x, logo_y, scaled_logo)
                        logo_loaded = True
                elif logo_path and os.path.exists(logo_path):
                    # تحميل اللوجو من المسار
                    logo_pixmap = QPixmap(logo_path)
                    if not logo_pixmap.isNull():
                        scaled_logo = logo_pixmap.scaled(logo_size, logo_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                        painter.drawPixmap(logo_x, logo_y, scaled_logo)
                        logo_loaded = True

        except Exception as e:
            print(f"خطأ في تحميل اللوجو: {e}")

        # إذا لم يتم تحميل اللوجو المخصص، استخدم الافتراضي
        if not logo_loaded:
            try:
                # محاولة تحميل اللوجو الافتراضي
                default_logo_path = resource_path(os.path.join('assets', 'company01_logo.png'))
                if os.path.exists(default_logo_path):
                    logo_pixmap = QPixmap(default_logo_path)
                    if not logo_pixmap.isNull():
                        scaled_logo = logo_pixmap.scaled(logo_size, logo_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                        painter.drawPixmap(logo_x, logo_y, scaled_logo)
                        logo_loaded = True
            except Exception as e:
                print(f"خطأ في تحميل اللوجو الافتراضي: {e}")

        # إذا فشل كل شيء، استخدم النص الافتراضي
        if not logo_loaded:
            painter.setPen(QPen(QColor(0, 0, 0), 2))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(logo_x, logo_y + 25, "🏢")
            painter.setFont(QFont("Arial", 10, QFont.Bold))
            painter.drawText(logo_x, logo_y + 40, company_name)


def show_new_design_print_dialog(engine, invoice_id, parent=None):
    """عرض نافذة الطباعة الجديدة"""
    dialog = NewDesignInvoicePrinter(engine, invoice_id, parent)
    dialog.exec_()
