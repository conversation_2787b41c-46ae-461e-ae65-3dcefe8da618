#!/usr/bin/env python3
"""
واجهة ويب مبسطة بدون Flask
"""

import http.server
import socketserver
import json
import os
import webbrowser
import threading
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from database.models import Transaction, Product, Customer, TransactionType


class SimpleWebInterface:
    """واجهة ويب مبسطة"""
    
    def __init__(self, engine, port=8080):
        self.engine = engine
        self.port = port
        self.server = None
        self.server_thread = None
    
    def generate_dashboard_html(self):
        """إنشاء صفحة HTML للوحة المعلومات"""
        try:
            # جلب البيانات بطريقة آمنة
            with Session(self.engine) as session:
                # إحصائيات أساسية
                try:
                    total_sales = session.query(Transaction).filter(
                        Transaction.type == TransactionType.SALE
                    ).count()
                except:
                    total_sales = 0

                try:
                    total_products = session.query(Product).count()
                except:
                    total_products = 0

                try:
                    total_customers = session.query(Customer).count()
                except:
                    total_customers = 0

                # آخر المعاملات بطريقة آمنة
                try:
                    recent_transactions = session.query(Transaction).order_by(
                        Transaction.date.desc()
                    ).limit(5).all()

                    # تحميل البيانات المرتبطة مسبقاً
                    for transaction in recent_transactions:
                        try:
                            # تحميل العميل إذا كان موجوداً
                            if transaction.customer_id:
                                customer = session.query(Customer).filter(
                                    Customer.id == transaction.customer_id
                                ).first()
                                transaction._customer_name = customer.name if customer else "غير محدد"
                            else:
                                transaction._customer_name = "غير محدد"
                        except:
                            transaction._customer_name = "غير محدد"

                except Exception as e:
                    print(f"خطأ في جلب المعاملات: {e}")
                    recent_transactions = []

        except Exception as e:
            print(f"خطأ في جلب البيانات: {e}")
            total_sales = 0
            total_products = 0
            total_customers = 0
            recent_transactions = []
        
        # إنشاء HTML
        html_content = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة - لوحة المعلومات</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            direction: rtl;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(45deg, #2C3E50, #3498DB);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px;
        }}
        
        .stat-card {{
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }}
        
        .stat-card:hover {{
            transform: translateY(-5px);
        }}
        
        .stat-card.sales {{
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }}
        
        .stat-card.products {{
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }}
        
        .stat-card.customers {{
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }}
        
        .stat-number {{
            font-size: 3em;
            font-weight: bold;
            margin: 10px 0;
        }}
        
        .stat-label {{
            font-size: 1.2em;
            opacity: 0.9;
        }}
        
        .transactions-section {{
            padding: 30px;
            background: #f8f9fa;
        }}
        
        .section-title {{
            font-size: 1.8em;
            margin-bottom: 20px;
            color: #2C3E50;
            border-bottom: 3px solid #3498DB;
            padding-bottom: 10px;
        }}
        
        .transaction-item {{
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-right: 4px solid #3498DB;
        }}
        
        .transaction-date {{
            color: #7f8c8d;
            font-size: 0.9em;
        }}
        
        .transaction-type {{
            font-weight: bold;
            color: #2C3E50;
        }}
        
        .footer {{
            background: #2C3E50;
            color: white;
            text-align: center;
            padding: 20px;
        }}
        
        .refresh-btn {{
            background: #27AE60;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 20px;
            transition: background 0.3s ease;
        }}
        
        .refresh-btn:hover {{
            background: #229954;
        }}
        
        @media (max-width: 768px) {{
            .stats-grid {{
                grid-template-columns: 1fr;
            }}
            
            .header h1 {{
                font-size: 2em;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 نظام المحاسبة العصري</h1>
            <p>لوحة المعلومات الرئيسية</p>
            <button class="refresh-btn" onclick="location.reload()">🔄 تحديث البيانات</button>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card sales">
                <div class="stat-number">{total_sales}</div>
                <div class="stat-label">📊 إجمالي المبيعات</div>
            </div>
            
            <div class="stat-card products">
                <div class="stat-number">{total_products}</div>
                <div class="stat-label">📦 المنتجات</div>
            </div>
            
            <div class="stat-card customers">
                <div class="stat-number">{total_customers}</div>
                <div class="stat-label">👥 العملاء</div>
            </div>
        </div>
        
        <div class="transactions-section">
            <h2 class="section-title">📋 آخر المعاملات</h2>
            
            {self.generate_transactions_html(recent_transactions)}
        </div>
        
        <div class="footer">
            <p>© 2025 نظام المحاسبة العصري - تم التحديث في {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
    </div>
    
    <script>
        // تحديث تلقائي كل 30 ثانية
        setTimeout(function() {{
            location.reload();
        }}, 30000);
        
        console.log('🚀 تم تحميل لوحة المعلومات بنجاح');
    </script>
</body>
</html>
        """
        
        return html_content
    
    def generate_transactions_html(self, transactions):
        """إنشاء HTML للمعاملات"""
        if not transactions:
            return '<div class="transaction-item">لا توجد معاملات حديثة</div>'

        html = ""
        for transaction in transactions:
            try:
                transaction_type = "مبيعات" if transaction.type == TransactionType.SALE else "مشتريات"

                # استخدام الاسم المحفوظ مسبقاً
                customer_name = getattr(transaction, '_customer_name', 'غير محدد')

                # تنسيق التاريخ بطريقة آمنة
                try:
                    date_str = transaction.date.strftime('%Y-%m-%d')
                except:
                    date_str = "غير محدد"

                # تنسيق المبلغ بطريقة آمنة
                try:
                    amount_str = f"{transaction.total_amount:,.2f}"
                except:
                    amount_str = "0.00"

                html += f"""
                <div class="transaction-item">
                    <div class="transaction-type">{transaction_type}</div>
                    <div class="transaction-date">📅 {date_str}</div>
                    <div>👤 العميل: {customer_name}</div>
                    <div>💰 المبلغ: {amount_str} ريال</div>
                </div>
                """
            except Exception as e:
                print(f"خطأ في عرض المعاملة: {e}")
                continue

        return html if html else '<div class="transaction-item">لا توجد معاملات صالحة للعرض</div>'
    
    def start_server(self):
        """بدء الخادم"""
        try:
            # إنشاء مجلد مؤقت للملفات
            temp_dir = os.path.join(os.path.dirname(__file__), 'temp')
            os.makedirs(temp_dir, exist_ok=True)
            
            # إنشاء ملف HTML
            html_content = self.generate_dashboard_html()
            html_file = os.path.join(temp_dir, 'dashboard.html')
            
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            # إنشاء خادم HTTP بسيط
            class CustomHandler(http.server.SimpleHTTPRequestHandler):
                def __init__(self, *args, **kwargs):
                    super().__init__(*args, directory=temp_dir, **kwargs)
                
                def log_message(self, format, *args):
                    # تجاهل رسائل السجل
                    pass
            
            # بدء الخادم
            with socketserver.TCPServer(("", self.port), CustomHandler) as httpd:
                self.server = httpd
                print(f"🌐 تم بدء خادم الويب على المنفذ {self.port}")
                print(f"🔗 افتح المتصفح على: http://localhost:{self.port}/dashboard.html")
                
                # فتح المتصفح تلقائياً
                webbrowser.open(f'http://localhost:{self.port}/dashboard.html')
                
                httpd.serve_forever()
                
        except Exception as e:
            print(f"❌ خطأ في بدء خادم الويب: {e}")
            return False
    
    def start_in_background(self):
        """بدء الخادم في الخلفية"""
        if self.server_thread and self.server_thread.is_alive():
            print("⚠️ الخادم يعمل بالفعل")
            return True
        
        try:
            self.server_thread = threading.Thread(target=self.start_server, daemon=True)
            self.server_thread.start()
            return True
        except Exception as e:
            print(f"❌ خطأ في بدء الخادم: {e}")
            return False
    
    def stop_server(self):
        """إيقاف الخادم"""
        if self.server:
            self.server.shutdown()
            self.server = None
            print("🛑 تم إيقاف خادم الويب")
    
    def is_running(self):
        """التحقق من حالة الخادم"""
        return self.server_thread and self.server_thread.is_alive()


def create_simple_web_interface(engine, port=8080):
    """إنشاء واجهة ويب مبسطة"""
    return SimpleWebInterface(engine, port)
