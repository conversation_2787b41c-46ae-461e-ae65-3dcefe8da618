#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار استيراد ملفات Excel للمنتجات
Test Excel import for products
"""

import pandas as pd
import os
from datetime import datetime

def create_sample_excel():
    """إنشاء ملف Excel تجريبي للمنتجات"""
    
    # بيانات تجريبية للمنتجات
    products_data = {
        'اسم المنتج': [
            'لابتوب ديل',
            'ماوس لوجيتك',
            'كيبورد ميكانيكي',
            'شاشة سامسونج 24 بوصة',
            'سماعات بلوتوث',
            'كاميرا ويب',
            'هارد ديسك خارجي 1TB',
            'ذاكرة USB 32GB',
            'كابل HDMI',
            'شاحن لابتوب'
        ],
        'كود المنتج': [
            'LAP001',
            'MOU001', 
            'KEY001',
            'MON001',
            'HEA001',
            'CAM001',
            'HDD001',
            'USB001',
            'CAB001',
            'CHR001'
        ],
        'سعر الشراء': [
            15000.00,
            150.00,
            800.00,
            3500.00,
            250.00,
            300.00,
            1200.00,
            80.00,
            50.00,
            400.00
        ],
        'سعر البيع': [
            18000.00,
            200.00,
            1000.00,
            4200.00,
            350.00,
            400.00,
            1500.00,
            120.00,
            80.00,
            500.00
        ],
        'الكمية': [
            5,
            20,
            15,
            8,
            12,
            10,
            6,
            25,
            30,
            7
        ],
        'الوحدة': [
            'قطعة',
            'قطعة',
            'قطعة',
            'قطعة',
            'قطعة',
            'قطعة',
            'قطعة',
            'قطعة',
            'قطعة',
            'قطعة'
        ],
        'الفئة': [
            'أجهزة كمبيوتر',
            'ملحقات',
            'ملحقات',
            'شاشات',
            'صوتيات',
            'ملحقات',
            'تخزين',
            'تخزين',
            'كابلات',
            'ملحقات'
        ],
        'الوصف': [
            'لابتوب ديل انسبايرون 15',
            'ماوس لاسلكي بتقنية البلوتوث',
            'كيبورد ميكانيكي للألعاب',
            'شاشة LED عالية الدقة',
            'سماعات بلوتوث مقاومة للماء',
            'كاميرا ويب عالية الدقة',
            'هارد ديسك خارجي محمول',
            'ذاكرة فلاش عالية السرعة',
            'كابل HDMI عالي الجودة',
            'شاحن أصلي للابتوب'
        ],
        'الحد الأدنى': [
            2,
            5,
            3,
            2,
            3,
            2,
            2,
            10,
            15,
            2
        ]
    }
    
    # إنشاء DataFrame
    df = pd.DataFrame(products_data)
    
    # حفظ الملف
    filename = f"منتجات_تجريبية_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='المنتجات', index=False)
    
    print(f"✅ تم إنشاء ملف Excel التجريبي: {filename}")
    print(f"📊 عدد المنتجات: {len(df)}")
    print(f"📋 الأعمدة: {', '.join(df.columns)}")
    
    return filename

def create_english_sample():
    """إنشاء ملف Excel بأسماء أعمدة إنجليزية"""
    
    products_data = {
        'Product Name': [
            'Dell Laptop',
            'Logitech Mouse',
            'Mechanical Keyboard',
            'Samsung Monitor 24"',
            'Bluetooth Headphones'
        ],
        'Product Code': [
            'LAP001',
            'MOU001', 
            'KEY001',
            'MON001',
            'HEA001'
        ],
        'Purchase Price': [
            15000.00,
            150.00,
            800.00,
            3500.00,
            250.00
        ],
        'Sale Price': [
            18000.00,
            200.00,
            1000.00,
            4200.00,
            350.00
        ],
        'Quantity': [
            5,
            20,
            15,
            8,
            12
        ],
        'Unit': [
            'Piece',
            'Piece',
            'Piece',
            'Piece',
            'Piece'
        ],
        'Category': [
            'Computers',
            'Accessories',
            'Accessories',
            'Monitors',
            'Audio'
        ]
    }
    
    df = pd.DataFrame(products_data)
    filename = f"products_english_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Products', index=False)
    
    print(f"✅ تم إنشاء ملف Excel الإنجليزي: {filename}")
    return filename

def create_mixed_sample():
    """إنشاء ملف Excel مختلط (عربي وإنجليزي)"""
    
    products_data = {
        'Name': [
            'لابتوب ديل',
            'Logitech Mouse',
            'كيبورد ميكانيكي'
        ],
        'Code': [
            'LAP001',
            'MOU001', 
            'KEY001'
        ],
        'Price': [
            15000.00,
            150.00,
            800.00
        ],
        'Stock': [
            5,
            20,
            15
        ]
    }
    
    df = pd.DataFrame(products_data)
    filename = f"products_mixed_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Sheet1', index=False)
    
    print(f"✅ تم إنشاء ملف Excel المختلط: {filename}")
    return filename

def test_import_detection():
    """اختبار تحديد نوع البيانات"""
    
    print("🧪 اختبار تحديد نوع البيانات من أسماء الأعمدة...")
    
    # محاكاة دالة التحديد
    def detect_data_type_from_columns(columns):
        columns = [col.lower() for col in columns]
        
        product_keywords = ['name', 'productname', 'item_name', 'اسم_المنتج', 'الاسم', 'اسم_الصنف',
                           'code', 'productcode', 'item_code', 'كود', 'الكود', 'كود_الصنف',
                           'price', 'sale_price', 'purchase_price', 'سعر', 'سعر_البيع', 'سعر_الشراء',
                           'quantity', 'stock', 'كمية', 'الكمية', 'المخزون']
        
        product_matches = sum(1 for col in columns if any(keyword in col for keyword in product_keywords))
        
        return 'products' if product_matches > 0 else None
    
    # اختبار أعمدة مختلفة
    test_cases = [
        ['اسم المنتج', 'كود المنتج', 'سعر البيع'],
        ['Product Name', 'Product Code', 'Sale Price'],
        ['Name', 'Code', 'Price', 'Stock'],
        ['العميل', 'الهاتف', 'العنوان'],
        ['Customer', 'Phone', 'Address']
    ]
    
    for i, columns in enumerate(test_cases):
        result = detect_data_type_from_columns(columns)
        print(f"اختبار {i+1}: {columns} -> {result}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار استيراد Excel للمنتجات")
    print("=" * 50)
    
    try:
        # إنشاء ملفات تجريبية
        print("📁 إنشاء ملفات Excel تجريبية...")
        
        arabic_file = create_sample_excel()
        english_file = create_english_sample()
        mixed_file = create_mixed_sample()
        
        print("\n" + "=" * 50)
        
        # اختبار تحديد نوع البيانات
        test_import_detection()
        
        print("\n" + "=" * 50)
        print("✅ تم إنشاء الملفات التجريبية بنجاح!")
        print("\nيمكنك الآن اختبار استيراد هذه الملفات في البرنامج:")
        print(f"1. {arabic_file}")
        print(f"2. {english_file}")
        print(f"3. {mixed_file}")
        
        print("\n📝 تعليمات الاختبار:")
        print("1. افتح البرنامج الرئيسي")
        print("2. اذهب إلى قائمة 'أدوات' -> 'استيراد من EasAcc'")
        print("3. اختر 'Excel' كنوع قاعدة البيانات")
        print("4. حدد أحد الملفات المنشأة")
        print("5. تحقق من ظهور البيانات في الجدول")
        print("6. اضغط 'استيراد البيانات المحددة'")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")

if __name__ == "__main__":
    main()
