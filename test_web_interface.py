#!/usr/bin/env python3
"""
اختبار واجهة الويب المبسطة
"""

import sys
import os
from sqlalchemy import create_engine

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_web_interface():
    """اختبار واجهة الويب"""
    print("🌐 اختبار واجهة الويب المبسطة...")
    print("=" * 50)
    
    try:
        # إعداد قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
        engine = create_engine(f'sqlite:///{db_path}', echo=False)
        print(f"✅ تم الاتصال بقاعدة البيانات: {db_path}")
        
        # استيراد واجهة الويب المبسطة
        from web.simple_web_interface import SimpleWebInterface
        print("✅ تم استيراد واجهة الويب المبسطة")
        
        # إنشاء واجهة الويب
        web_interface = SimpleWebInterface(engine, port=8080)
        print("✅ تم إنشاء واجهة الويب")
        
        # اختبار إنشاء HTML
        html_content = web_interface.generate_dashboard_html()
        print(f"✅ تم إنشاء HTML بنجاح ({len(html_content)} حرف)")
        
        # حفظ HTML للاختبار
        test_file = os.path.join(os.path.dirname(__file__), 'test_dashboard.html')
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"✅ تم حفظ ملف الاختبار: {test_file}")
        
        # بدء الخادم في الخلفية
        print("🚀 بدء خادم الويب...")
        success = web_interface.start_in_background()
        
        if success:
            print("✅ تم بدء خادم الويب بنجاح!")
            print(f"🔗 افتح المتصفح على: http://localhost:8080/dashboard.html")
            print("⏰ الخادم يعمل في الخلفية...")
            print("🔄 سيتم تحديث الصفحة تلقائياً كل 30 ثانية")
            print("")
            print("📋 للاختبار:")
            print("  1. افتح المتصفح على الرابط أعلاه")
            print("  2. تحقق من عرض الإحصائيات")
            print("  3. تحقق من عرض آخر المعاملات")
            print("  4. جرب زر 'تحديث البيانات'")
            print("")
            print("⚠️ لإيقاف الخادم، أغلق هذا البرنامج")
            
            # انتظار إدخال المستخدم لإيقاف الخادم
            input("اضغط Enter لإيقاف الخادم...")
            
            web_interface.stop_server()
            print("🛑 تم إيقاف خادم الويب")
            
        else:
            print("❌ فشل في بدء خادم الويب")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة الويب: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_web_interface()
