
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة - لوحة المعلومات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #2C3E50, #3498DB);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card.sales {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .stat-card.products {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .stat-card.customers {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        
        .stat-number {
            font-size: 3em;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .stat-label {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .transactions-section {
            padding: 30px;
            background: #f8f9fa;
        }
        
        .section-title {
            font-size: 1.8em;
            margin-bottom: 20px;
            color: #2C3E50;
            border-bottom: 3px solid #3498DB;
            padding-bottom: 10px;
        }
        
        .transaction-item {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-right: 4px solid #3498DB;
        }
        
        .transaction-date {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        
        .transaction-type {
            font-weight: bold;
            color: #2C3E50;
        }
        
        .footer {
            background: #2C3E50;
            color: white;
            text-align: center;
            padding: 20px;
        }
        
        .refresh-btn {
            background: #27AE60;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 20px;
            transition: background 0.3s ease;
        }
        
        .refresh-btn:hover {
            background: #229954;
        }
        
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 نظام المحاسبة العصري</h1>
            <p>لوحة المعلومات الرئيسية</p>
            <button class="refresh-btn" onclick="location.reload()">🔄 تحديث البيانات</button>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card sales">
                <div class="stat-number">2</div>
                <div class="stat-label">📊 إجمالي المبيعات</div>
            </div>
            
            <div class="stat-card products">
                <div class="stat-number">2</div>
                <div class="stat-label">📦 المنتجات</div>
            </div>
            
            <div class="stat-card customers">
                <div class="stat-number">1</div>
                <div class="stat-label">👥 العملاء</div>
            </div>
        </div>
        
        <div class="transactions-section">
            <h2 class="section-title">📋 آخر المعاملات</h2>
            
            
                <div class="transaction-item">
                    <div class="transaction-type">مبيعات</div>
                    <div class="transaction-date">📅 2025-06-26</div>
                    <div>👤 العميل: غير محدد</div>
                    <div>💰 المبلغ: 4,100.00 ريال</div>
                </div>
                
                <div class="transaction-item">
                    <div class="transaction-type">مبيعات</div>
                    <div class="transaction-date">📅 2025-06-26</div>
                    <div>👤 العميل: غير محدد</div>
                    <div>💰 المبلغ: 4,100.00 ريال</div>
                </div>
                
                <div class="transaction-item">
                    <div class="transaction-type">مشتريات</div>
                    <div class="transaction-date">📅 2025-06-26</div>
                    <div>👤 العميل: غير محدد</div>
                    <div>💰 المبلغ: 2,000.00 ريال</div>
                </div>
                
        </div>
        
        <div class="footer">
            <p>© 2025 نظام المحاسبة العصري - تم التحديث في 2025-07-12 22:28:14</p>
        </div>
    </div>
    
    <script>
        // تحديث تلقائي كل 30 ثانية
        setTimeout(function() {
            location.reload();
        }, 30000);
        
        console.log('🚀 تم تحميل لوحة المعلومات بنجاح');
    </script>
</body>
</html>
        