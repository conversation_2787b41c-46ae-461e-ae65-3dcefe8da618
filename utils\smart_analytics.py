#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التحليلات الذكية - تحليل البيانات والتنبؤات
Smart Analytics System - Data analysis and predictions
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QFrame, QScrollArea, QGroupBox,
                             QFormLayout, QComboBox, QDateEdit, QTabWidget,
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QProgressBar, QTextEdit)
from PyQt5.QtCore import QTimer, QThread, pyqtSignal, Qt, QDate
from PyQt5.QtGui import QFont, QColor
from PyQt5.QtChart import (QChart, QChartView, QLineSeries, QBarSeries, QBarSet, 
                          QValueAxis, QBarCategoryAxis, QPieSeries, QAreaSeries,
                          QScatterSeries, QSplineSeries)
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, desc, text
from database.models import Transaction, TransactionItem, Product, Customer, Supplier, TransactionType
from utils.currency_formatter import format_currency, format_number
from utils.performance_optimizer import DatabaseOptimizer, cache_manager
from datetime import datetime, timedelta
import json
import statistics
import math


class AnalyticsEngine(QThread):
    """محرك التحليلات في خيط منفصل"""
    
    analysis_completed = pyqtSignal(dict)
    progress_updated = pyqtSignal(int, str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, engine, analysis_type, parameters):
        super().__init__()
        self.engine = engine
        self.analysis_type = analysis_type
        self.parameters = parameters
        
    def run(self):
        """تشغيل التحليل"""
        try:
            if self.analysis_type == "sales_trends":
                result = self.analyze_sales_trends()
            elif self.analysis_type == "customer_behavior":
                result = self.analyze_customer_behavior()
            elif self.analysis_type == "product_performance":
                result = self.analyze_product_performance()
            elif self.analysis_type == "financial_health":
                result = self.analyze_financial_health()
            elif self.analysis_type == "predictions":
                result = self.generate_predictions()
            else:
                result = {"error": "نوع تحليل غير مدعوم"}
            
            self.analysis_completed.emit(result)
            
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def analyze_sales_trends(self):
        """تحليل اتجاهات المبيعات"""
        self.progress_updated.emit(10, "تحليل اتجاهات المبيعات...")
        
        with Session(self.engine) as session:
            # الحصول على بيانات المبيعات الشهرية
            monthly_sales = session.execute(text("""
                SELECT 
                    strftime('%Y-%m', date) as month,
                    SUM(total_amount) as total_sales,
                    COUNT(*) as transaction_count,
                    AVG(total_amount) as avg_transaction
                FROM transactions 
                WHERE type = 'بيع' 
                    AND date >= date('now', '-12 months')
                GROUP BY strftime('%Y-%m', date)
                ORDER BY month
            """)).fetchall()
            
            self.progress_updated.emit(30, "حساب معدلات النمو...")
            
            # حساب معدل النمو
            growth_rates = []
            sales_data = []
            
            for i, row in enumerate(monthly_sales):
                sales_data.append({
                    'month': row.month,
                    'total_sales': float(row.total_sales),
                    'transaction_count': row.transaction_count,
                    'avg_transaction': float(row.avg_transaction)
                })
                
                if i > 0:
                    prev_sales = monthly_sales[i-1].total_sales
                    current_sales = row.total_sales
                    growth_rate = ((current_sales - prev_sales) / prev_sales) * 100
                    growth_rates.append(growth_rate)
            
            self.progress_updated.emit(50, "تحليل الموسمية...")
            
            # تحليل الموسمية
            seasonal_analysis = self.analyze_seasonality(sales_data)
            
            self.progress_updated.emit(70, "حساب الإحصائيات...")
            
            # إحصائيات عامة
            total_sales = sum(row['total_sales'] for row in sales_data)
            avg_monthly_sales = total_sales / len(sales_data) if sales_data else 0
            avg_growth_rate = statistics.mean(growth_rates) if growth_rates else 0
            
            self.progress_updated.emit(100, "اكتمل التحليل")
            
            return {
                'type': 'sales_trends',
                'monthly_data': sales_data,
                'growth_rates': growth_rates,
                'seasonal_analysis': seasonal_analysis,
                'summary': {
                    'total_sales': total_sales,
                    'avg_monthly_sales': avg_monthly_sales,
                    'avg_growth_rate': avg_growth_rate,
                    'best_month': max(sales_data, key=lambda x: x['total_sales']) if sales_data else None,
                    'worst_month': min(sales_data, key=lambda x: x['total_sales']) if sales_data else None
                }
            }
    
    def analyze_customer_behavior(self):
        """تحليل سلوك العملاء"""
        self.progress_updated.emit(10, "تحليل سلوك العملاء...")
        
        with Session(self.engine) as session:
            # تحليل تكرار الشراء
            customer_frequency = session.execute(text("""
                SELECT 
                    c.name,
                    COUNT(t.id) as purchase_count,
                    SUM(t.total_amount) as total_spent,
                    AVG(t.total_amount) as avg_purchase,
                    MAX(t.date) as last_purchase,
                    MIN(t.date) as first_purchase
                FROM customers c
                JOIN transactions t ON c.id = t.customer_id
                WHERE t.type = 'بيع'
                GROUP BY c.id, c.name
                ORDER BY total_spent DESC
                LIMIT 50
            """)).fetchall()
            
            self.progress_updated.emit(40, "تصنيف العملاء...")
            
            # تصنيف العملاء (RFM Analysis)
            customer_segments = self.segment_customers(customer_frequency)
            
            self.progress_updated.emit(70, "تحليل أنماط الشراء...")
            
            # تحليل أنماط الشراء
            purchase_patterns = session.execute(text("""
                SELECT 
                    strftime('%w', date) as day_of_week,
                    strftime('%H', date) as hour_of_day,
                    COUNT(*) as transaction_count,
                    AVG(total_amount) as avg_amount
                FROM transactions 
                WHERE type = 'بيع'
                    AND date >= date('now', '-3 months')
                GROUP BY strftime('%w', date), strftime('%H', date)
                ORDER BY transaction_count DESC
            """)).fetchall()
            
            self.progress_updated.emit(100, "اكتمل التحليل")
            
            return {
                'type': 'customer_behavior',
                'customer_frequency': [dict(row._mapping) for row in customer_frequency],
                'customer_segments': customer_segments,
                'purchase_patterns': [dict(row._mapping) for row in purchase_patterns],
                'insights': self.generate_customer_insights(customer_frequency, customer_segments)
            }
    
    def analyze_product_performance(self):
        """تحليل أداء المنتجات"""
        self.progress_updated.emit(10, "تحليل أداء المنتجات...")
        
        with Session(self.engine) as session:
            # أداء المنتجات
            product_performance = session.execute(text("""
                SELECT 
                    p.name,
                    p.code,
                    p.category,
                    SUM(ti.quantity) as total_sold,
                    SUM(ti.quantity * ti.unit_price) as total_revenue,
                    AVG(ti.unit_price) as avg_price,
                    COUNT(DISTINCT t.id) as transaction_count,
                    p.quantity as current_stock,
                    p.min_quantity
                FROM products p
                JOIN transaction_items ti ON p.id = ti.product_id
                JOIN transactions t ON ti.transaction_id = t.id
                WHERE t.type = 'بيع'
                    AND t.date >= date('now', '-6 months')
                GROUP BY p.id, p.name, p.code, p.category
                ORDER BY total_revenue DESC
                LIMIT 100
            """)).fetchall()
            
            self.progress_updated.emit(50, "تحليل الربحية...")
            
            # تحليل الربحية
            profitability_analysis = self.analyze_product_profitability(product_performance)
            
            self.progress_updated.emit(80, "تحديد المنتجات الراكدة...")
            
            # المنتجات الراكدة
            slow_moving = session.execute(text("""
                SELECT 
                    p.name,
                    p.code,
                    p.quantity,
                    COALESCE(last_sale.last_sale_date, 'لم يباع مطلقاً') as last_sale_date,
                    COALESCE(total_sales.total_sold, 0) as total_sold_ever
                FROM products p
                LEFT JOIN (
                    SELECT 
                        ti.product_id,
                        MAX(t.date) as last_sale_date
                    FROM transaction_items ti
                    JOIN transactions t ON ti.transaction_id = t.id
                    WHERE t.type = 'بيع'
                    GROUP BY ti.product_id
                ) last_sale ON p.id = last_sale.product_id
                LEFT JOIN (
                    SELECT 
                        ti.product_id,
                        SUM(ti.quantity) as total_sold
                    FROM transaction_items ti
                    JOIN transactions t ON ti.transaction_id = t.id
                    WHERE t.type = 'بيع'
                    GROUP BY ti.product_id
                ) total_sales ON p.id = total_sales.product_id
                WHERE p.is_active = 1
                    AND (last_sale.last_sale_date IS NULL 
                         OR last_sale.last_sale_date < date('now', '-3 months'))
                ORDER BY p.quantity DESC
                LIMIT 50
            """)).fetchall()
            
            self.progress_updated.emit(100, "اكتمل التحليل")
            
            return {
                'type': 'product_performance',
                'product_performance': [dict(row._mapping) for row in product_performance],
                'profitability_analysis': profitability_analysis,
                'slow_moving_products': [dict(row._mapping) for row in slow_moving],
                'recommendations': self.generate_product_recommendations(product_performance, slow_moving)
            }
    
    def analyze_financial_health(self):
        """تحليل الصحة المالية"""
        self.progress_updated.emit(10, "تحليل الصحة المالية...")
        
        with Session(self.engine) as session:
            # المؤشرات المالية الأساسية
            financial_metrics = session.execute(text("""
                SELECT 
                    strftime('%Y-%m', date) as month,
                    SUM(CASE WHEN type = 'بيع' THEN total_amount ELSE 0 END) as revenue,
                    SUM(CASE WHEN type = 'شراء' THEN total_amount ELSE 0 END) as costs,
                    SUM(CASE WHEN type = 'مصروف' THEN total_amount ELSE 0 END) as expenses,
                    (SUM(CASE WHEN type = 'بيع' THEN total_amount ELSE 0 END) - 
                     SUM(CASE WHEN type = 'شراء' THEN total_amount ELSE 0 END) - 
                     SUM(CASE WHEN type = 'مصروف' THEN total_amount ELSE 0 END)) as net_profit
                FROM transactions 
                WHERE date >= date('now', '-12 months')
                GROUP BY strftime('%Y-%m', date)
                ORDER BY month
            """)).fetchall()
            
            self.progress_updated.emit(50, "حساب النسب المالية...")
            
            # حساب النسب المالية
            financial_ratios = self.calculate_financial_ratios(financial_metrics)
            
            self.progress_updated.emit(80, "تقييم الاتجاهات...")
            
            # تقييم الاتجاهات
            trends_analysis = self.analyze_financial_trends(financial_metrics)
            
            self.progress_updated.emit(100, "اكتمل التحليل")
            
            return {
                'type': 'financial_health',
                'monthly_metrics': [dict(row._mapping) for row in financial_metrics],
                'financial_ratios': financial_ratios,
                'trends_analysis': trends_analysis,
                'health_score': self.calculate_health_score(financial_ratios, trends_analysis)
            }
    
    def generate_predictions(self):
        """توليد التنبؤات"""
        self.progress_updated.emit(10, "توليد التنبؤات...")
        
        # تنبؤات المبيعات
        sales_prediction = self.predict_sales()
        self.progress_updated.emit(40, "تنبؤ احتياجات المخزون...")
        
        # تنبؤات المخزون
        inventory_prediction = self.predict_inventory_needs()
        self.progress_updated.emit(70, "تنبؤ سلوك العملاء...")
        
        # تنبؤات العملاء
        customer_prediction = self.predict_customer_behavior()
        self.progress_updated.emit(100, "اكتمل التحليل")
        
        return {
            'type': 'predictions',
            'sales_prediction': sales_prediction,
            'inventory_prediction': inventory_prediction,
            'customer_prediction': customer_prediction
        }
    
    def analyze_seasonality(self, sales_data):
        """تحليل الموسمية"""
        # تحليل بسيط للموسمية بناءً على الشهور
        monthly_averages = {}
        
        for data in sales_data:
            month = data['month'].split('-')[1]  # استخراج الشهر
            if month not in monthly_averages:
                monthly_averages[month] = []
            monthly_averages[month].append(data['total_sales'])
        
        # حساب المتوسطات
        seasonal_factors = {}
        for month, sales in monthly_averages.items():
            seasonal_factors[month] = statistics.mean(sales)
        
        return seasonal_factors
    
    def segment_customers(self, customer_data):
        """تصنيف العملاء باستخدام RFM"""
        segments = {
            'champions': [],      # عملاء ممتازون
            'loyal': [],          # عملاء مخلصون
            'potential': [],      # عملاء محتملون
            'at_risk': [],        # عملاء في خطر
            'lost': []            # عملاء مفقودون
        }
        
        for customer in customer_data:
            # حساب الأيام منذ آخر شراء
            last_purchase = datetime.strptime(customer.last_purchase, '%Y-%m-%d')
            days_since_last = (datetime.now() - last_purchase).days
            
            # تصنيف بسيط
            if days_since_last <= 30 and customer.purchase_count >= 5:
                segments['champions'].append(dict(customer._mapping))
            elif days_since_last <= 60 and customer.purchase_count >= 3:
                segments['loyal'].append(dict(customer._mapping))
            elif days_since_last <= 90:
                segments['potential'].append(dict(customer._mapping))
            elif days_since_last <= 180:
                segments['at_risk'].append(dict(customer._mapping))
            else:
                segments['lost'].append(dict(customer._mapping))
        
        return segments
    
    def generate_customer_insights(self, customer_frequency, customer_segments):
        """توليد رؤى العملاء"""
        insights = []
        
        # إحصائيات عامة
        total_customers = len(customer_frequency)
        champions_count = len(customer_segments['champions'])
        at_risk_count = len(customer_segments['at_risk'])
        
        if champions_count > 0:
            insights.append(f"لديك {champions_count} عميل ممتاز من أصل {total_customers}")
        
        if at_risk_count > 0:
            insights.append(f"تحذير: {at_risk_count} عميل في خطر الفقدان")
        
        return insights
    
    def analyze_product_profitability(self, product_data):
        """تحليل ربحية المنتجات"""
        # تحليل بسيط للربحية
        profitability = []
        
        for product in product_data:
            # حساب هامش الربح التقديري (بافتراض تكلفة 70% من سعر البيع)
            estimated_cost = product.total_revenue * 0.7
            estimated_profit = product.total_revenue - estimated_cost
            profit_margin = (estimated_profit / product.total_revenue) * 100 if product.total_revenue > 0 else 0
            
            profitability.append({
                'product_name': product.name,
                'revenue': float(product.total_revenue),
                'estimated_profit': estimated_profit,
                'profit_margin': profit_margin,
                'units_sold': product.total_sold
            })
        
        return sorted(profitability, key=lambda x: x['estimated_profit'], reverse=True)
    
    def generate_product_recommendations(self, performance_data, slow_moving_data):
        """توليد توصيات المنتجات"""
        recommendations = []
        
        # توصيات للمنتجات عالية الأداء
        if performance_data:
            top_product = performance_data[0]
            recommendations.append(f"المنتج الأكثر مبيعاً: {top_product.name} - يُنصح بزيادة المخزون")
        
        # توصيات للمنتجات الراكدة
        if len(slow_moving_data) > 5:
            recommendations.append(f"لديك {len(slow_moving_data)} منتج راكد - يُنصح بعمل عروض ترويجية")
        
        return recommendations
    
    def calculate_financial_ratios(self, financial_data):
        """حساب النسب المالية"""
        if not financial_data:
            return {}
        
        latest_month = financial_data[-1]
        
        # نسب أساسية
        ratios = {
            'profit_margin': (latest_month.net_profit / latest_month.revenue * 100) if latest_month.revenue > 0 else 0,
            'expense_ratio': (latest_month.expenses / latest_month.revenue * 100) if latest_month.revenue > 0 else 0,
            'cost_ratio': (latest_month.costs / latest_month.revenue * 100) if latest_month.revenue > 0 else 0
        }
        
        return ratios
    
    def analyze_financial_trends(self, financial_data):
        """تحليل الاتجاهات المالية"""
        if len(financial_data) < 2:
            return {}
        
        # مقارنة آخر شهرين
        current = financial_data[-1]
        previous = financial_data[-2]
        
        trends = {
            'revenue_trend': ((current.revenue - previous.revenue) / previous.revenue * 100) if previous.revenue > 0 else 0,
            'profit_trend': ((current.net_profit - previous.net_profit) / abs(previous.net_profit) * 100) if previous.net_profit != 0 else 0,
            'expense_trend': ((current.expenses - previous.expenses) / previous.expenses * 100) if previous.expenses > 0 else 0
        }
        
        return trends
    
    def calculate_health_score(self, ratios, trends):
        """حساب نقاط الصحة المالية"""
        score = 50  # نقطة البداية
        
        # تقييم هامش الربح
        if ratios.get('profit_margin', 0) > 20:
            score += 20
        elif ratios.get('profit_margin', 0) > 10:
            score += 10
        elif ratios.get('profit_margin', 0) < 0:
            score -= 20
        
        # تقييم اتجاه الإيرادات
        if trends.get('revenue_trend', 0) > 10:
            score += 15
        elif trends.get('revenue_trend', 0) > 0:
            score += 5
        elif trends.get('revenue_trend', 0) < -10:
            score -= 15
        
        # تقييم اتجاه الأرباح
        if trends.get('profit_trend', 0) > 0:
            score += 15
        elif trends.get('profit_trend', 0) < -20:
            score -= 20
        
        return max(0, min(100, score))  # ضمان أن النقاط بين 0 و 100
    
    def predict_sales(self):
        """تنبؤ المبيعات البسيط"""
        # تنبؤ بسيط بناءً على المتوسط المتحرك
        return {
            'next_month_prediction': 'تحتاج لبيانات أكثر للتنبؤ الدقيق',
            'confidence': 'متوسط',
            'method': 'المتوسط المتحرك البسيط'
        }
    
    def predict_inventory_needs(self):
        """تنبؤ احتياجات المخزون"""
        return {
            'reorder_recommendations': [],
            'stock_alerts': [],
            'seasonal_adjustments': []
        }
    
    def predict_customer_behavior(self):
        """تنبؤ سلوك العملاء"""
        return {
            'churn_risk': [],
            'upsell_opportunities': [],
            'retention_strategies': []
        }
