#!/usr/bin/env python3
"""
اختبار استيراد البيانات
"""

import os
import sys
import pandas as pd
from sqlalchemy import create_engine
from sqlalchemy.orm import Session

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.models import Product

def create_test_excel():
    """إنشاء ملف Excel تجريبي للاختبار"""
    print("📝 إنشاء ملف Excel تجريبي...")
    
    # بيانات تجريبية للمنتجات
    products_data = {
        'Name': ['منتج تجريبي 1', 'منتج تجريبي 2', 'منتج تجريبي 3'],
        'Code': ['TEST001', 'TEST002', 'TEST003'],
        'Purchase_Price': [50.0, 75.0, 100.0],
        'Sale_Price': [75.0, 100.0, 150.0],
        'Quantity': [100, 50, 25],
        'Unit': ['قطعة', 'كيلو', 'متر'],
        'Category': ['اختبار', 'اختبار', 'اختبار'],
        'Description': ['منتج للاختبار 1', 'منتج للاختبار 2', 'منتج للاختبار 3']
    }
    
    df = pd.DataFrame(products_data)
    
    # حفظ الملف
    test_file = os.path.join(os.path.dirname(__file__), 'test_products.xlsx')
    df.to_excel(test_file, index=False, sheet_name='Products')
    
    print(f"✅ تم إنشاء الملف: {test_file}")
    return test_file

def test_import_process():
    """اختبار عملية الاستيراد"""
    print("🧪 اختبار عملية استيراد البيانات...")
    print("=" * 50)
    
    try:
        # إنشاء ملف تجريبي
        test_file = create_test_excel()
        
        # الاتصال بقاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
        engine = create_engine(f'sqlite:///{db_path}', echo=False)
        print(f"✅ تم الاتصال بقاعدة البيانات: {db_path}")
        
        # عد المنتجات قبل الاستيراد
        with Session(engine) as session:
            before_count = session.query(Product).count()
            print(f"📊 عدد المنتجات قبل الاستيراد: {before_count}")
        
        # محاكاة عملية الاستيراد
        print("🔄 محاكاة عملية الاستيراد...")
        
        # قراءة البيانات من Excel
        df = pd.read_excel(test_file)
        print(f"📖 تم قراءة {len(df)} منتج من الملف")
        
        # تحويل البيانات مباشرة
        imported_count = 0
        error_count = 0

        with Session(engine) as session:
            for index, row in df.iterrows():
                try:
                    # تحويل البيانات إلى قاموس
                    product_data = row.to_dict()

                    # تحويل إلى منتج مباشرة
                    product = Product(
                        name=product_data.get('Name', 'منتج غير محدد'),
                        code=product_data.get('Code', f"PRD_{index}"),
                        purchase_price=float(product_data.get('Purchase_Price', 0)),
                        sale_price=float(product_data.get('Sale_Price', 0)),
                        quantity=int(product_data.get('Quantity', 0)),
                        unit=product_data.get('Unit', 'قطعة'),
                        category=product_data.get('Category', None),
                        description=product_data.get('Description', None),
                        is_active=True
                    )
                    
                    # التحقق من عدم وجود المنتج
                    existing = session.query(Product).filter(
                        Product.name == product.name
                    ).first()
                    
                    if not existing:
                        session.add(product)
                        imported_count += 1
                        print(f"✅ تم إضافة المنتج: {product.name}")
                    else:
                        print(f"⚠️ المنتج موجود مسبقاً: {product.name}")
                        
                except Exception as e:
                    error_count += 1
                    print(f"❌ خطأ في المنتج {index + 1}: {e}")
            
            # حفظ التغييرات
            session.commit()
        
        # عد المنتجات بعد الاستيراد
        with Session(engine) as session:
            after_count = session.query(Product).count()
            print(f"📊 عدد المنتجات بعد الاستيراد: {after_count}")
        
        print("\n" + "=" * 50)
        print("📊 ملخص الاختبار:")
        print(f"✅ تم الاستيراد: {imported_count}")
        print(f"❌ أخطاء: {error_count}")
        print(f"📈 الزيادة في المنتجات: {after_count - before_count}")
        
        if imported_count > 0:
            print("✅ اختبار الاستيراد نجح!")
        else:
            print("⚠️ لم يتم استيراد أي منتجات جديدة")
        
        # تنظيف الملف التجريبي
        try:
            os.remove(test_file)
            print("🗑️ تم حذف الملف التجريبي")
        except:
            pass
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستيراد: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_import_process()
